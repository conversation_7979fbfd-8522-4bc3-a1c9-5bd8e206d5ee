const SentryWebpackPlugin = require('@sentry/webpack-plugin')
const packageFile = require('./package.json')
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

console.log('process.env.NODE_ENV', process.env.NODE_ENV)
const plugins = [
    new SentryWebpackPlugin({
        include: "./dist",
        release: packageFile.version, // 版本号
        ignoreFile: ".sentrycliignore",
        ignore: ["node_modules", "vue.config.js"],
        configFile: "sentry.properties"
    }),
    // new UglifyJsPlugin({
    //   uglifyOptions: {
    //     compress: {
    //       drop_console: true,
    //       drop_debugger: true,
    //       pure_funcs: ['console.log']
    //     }
    //   }
    // })
]

module.exports = {
    devServer: {
        host: '0.0.0.0',
        port: 9007, // 端口号
        open: true, //配置自动启动浏览器 
    },
    lintOnSave: true,
    chainWebpack: (config) => {
        //忽略的打包文件
        config.externals({
            // 'vue': 'Vue',
            // 'vue-router': 'VueRouter',
            // 'vuex': 'Vuex',
            // 'axios': 'axios',
            // 'element-ui': 'ELEMENT',
        })

        const version = new Date().getTime()
        config.output.filename(`[name].${version}.js`).end()
        config.output.chunkFilename(`[name].${version}.js`).end()

        // 删除预加载
        config.plugins.delete('preload');
        config.plugins.delete('prefetch');
        // 压缩代码
        if (process.env.NODE_ENV === 'product' || process.env.NODE_ENV === 'azureprod') {
            config.optimization.minimize(true);
        } else {
            config.optimization.minimize(false);
        }
        // 分割代码
        config.optimization.splitChunks({
            chunks: 'all'
        })
    },
    // configureWebpack: {
    //     optimization: {
    //         usedExports: true,
    //     },
    //     // 开启sourcemap
    //     plugins: process.env.NODE_ENV === 'product' || process.env.NODE_ENV === 'azureprod' ? plugins : [],
    //     devtool: 'source-map'
    // },
}
