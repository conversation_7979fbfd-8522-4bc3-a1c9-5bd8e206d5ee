<template>
  <div class="address">
    <!-- <el-cascader v-model="selectedOptions" :placeholder="placeholder" :options="options" :props="cascaderProps"
      @change="handleChange"></el-cascader> -->
    <el-cascader v-model="selectedOptions" :options="provincelist" :props="props" @change="handleChange" :placeholder="placeholder" filterable style="width: 100%;"></el-cascader>
    <el-input style="margin-top: 5px" placeholder="请输入地址..." type="text" v-model="addressInfo.labAddress"
      maxlength="200"></el-input>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { ajaxNew } from "./../api/index";

@Component({
  name: "Address",
})
class Address extends Vue {
  @Prop() private addressProps;
  @Watch("addressProps", { immediate: true, deep: true })
  getProps(nVal) {
    if (nVal && nVal.province) {
      this.addressInfo = nVal;
      this.placeholder = `${nVal.province}${nVal.city}${nVal.town}`;
    } else {
      this.addressInfo = {
        province: "",
        city: "",
        town: "",
        labAddress: "",
      };
      this.placeholder = "请选择...";
      this.selectedOptions = []
    }
  }

  private placeholder = "";
  private options = [];
  private addressInfo = {
    province: "",
    city: "",
    town: "",
    labAddress: "",
  };
  private cascaderProps = {
    lazy: true,
    label: "orgName",
    value: "orgName",
    lazyLoad: (node, resolve) => {
      this.lazyLoad(node, resolve);
    },
  } as any;
  private selectedOptions = []
  private provincelist = []
  private props = {
    value: 'orgName',
    label: 'orgName',
    children: 'citys'
  }

  lazyLoad(node, resolve) {
    if (!node.root) this.addressQry(node.data.orgId, resolve);
  }

  // 获取省份区
  addressQry(parentOrgId, resolve?) {
    return ajaxNew("/business/api.v1.center/org/qry", { parentOrgId }, "post")
      .then((res) => {
        if (res.resultCode === "0") {
          if (!parentOrgId) {
            this.options = res.data.items;
          } else {
            res.data.items.forEach((item) => {
              item.leaf = item.orgType > 2;
            });
            resolve(res.data.items);
          }
        } else {
          this["$message"].error(res.resultMsg);
        }
      })
      .catch((e) => {
        this["$message"].error(e);
      });
  }

  handleChange(v) {
    this.addressInfo.province = v[0];
    this.addressInfo.city = v[1];
    this.addressInfo.town = v[2];
  }

  mounted() {
    // this.addressQry(0);
    ajaxNew("/business/api.v1.center/org/qryOrgTree", {}, "post")
      .then((res) => {
        this.provincelist = res.data
        debugger
      })
      .catch((e) => {
        this["$message"].error(e);
      });
  }

  // 监听用户选择和输入
  @Watch("addressInfo", { immediate: true, deep: true })
  getAddressInfo(nVal) {
    if (nVal.province || nVal.address) {
      this["$emit"]("emitAddress", this.addressInfo);
    }
  }
}
export default Address;
</script>
