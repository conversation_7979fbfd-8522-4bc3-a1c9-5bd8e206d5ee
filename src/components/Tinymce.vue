<template>
  <div>
    <editor :id="id"
            ref="tinymceEditor"
            v-model="value"
            :init="init"
            :setting="{inline: false}"
            @input="onEditorInput"></editor>
  </div>
</template>

<script lang="ts">
import axios from 'axios'
import { Component, Prop, Vue } from 'vue-property-decorator'
import tinymce from 'tinymce'
import { apiHost } from './../assets/javascript/util'

import 'tinymce/themes/silver/theme.min.js'
import 'tinymce/icons/default/icons'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/codesample'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/media'
import 'tinymce/plugins/emoticons'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/pagebreak'
import 'tinymce/plugins/insertdatetime'
import 'tinymce/plugins/hr'
import 'tinymce/plugins/paste'

import Editor from '@tinymce/tinymce-vue'

@Component({ name: 'TinymceEditor', components: { Editor } })
export default class extends Vue {
  @Prop({ default: '' }) content!: string
  @Prop() private height
  @Prop({ default: 'selector' }) id!: string
  @Prop({
    default:
      'codesample image bold italic underline alignleft aligncenter alignright alignjustify | forecolor backcolor | fontselect | fontsizeselect | formatselect |  bullist numlist | outdent indent blockquote | removeformat| undo redo | link unlink media insertdatetime table  hr pagebreak | fullscreen preview | strikethrough',
  })
  toolbar!: string
  @Prop({
    default:
      'link lists image codesample code table wordcount media table fullscreen preview pagebreak insertdatetime hr paste',
  })
  plugins!: string

  // 富文本框值
  private value = ''

  // 富文本框init配置
  private get init() {
    return {
      selector: '#' + this.id,
      language_url: '/tinymce/langs/zh_CN.js',
      language: 'zh_CN',
      skin_url: '/tinymce/skins/ui/oxide',
      height: this.height || 600,
      branding: false,
      plugins: 'paste', // 插件
      toolbar: this.toolbar, // 工具条
      font_formats:
        'Arial=arial,helvetica,sans-serif; 宋体=SimSun;  微软雅黑=Microsoft Yahei; Impact=impact,chicago;', // 字体
      fontsize_formats: '11px 12px 14px 16px 18px 24px 36px 48px 64px 72px', // 文字大小
      paste_data_images: true,
      paste_as_text: true,
      codesample_languages: [
        { text: 'HTML/XML', value: 'markup' },
        { text: 'JavaScript', value: 'javascript' },
        { text: 'CSS', value: 'css' },
        { text: 'Java', value: 'java' },
        { text: 'C++', value: 'cpp' },
      ],
      // 图片上传回调
      images_upload_handler: (blobInfo, success) => {
        const form = new FormData()
        form.append('file', blobInfo.blob(), blobInfo.filename())
        axios({
          method: 'post',
          url:
            apiHost + 'ticCenter/business/api.v0.platform/fileUpload/uploadOss',
          data: form,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
          .then((res) => {
            if (res.data.resultCode === '0') {
              success(res.data.data.fileName)
            }
          })
          .catch((e) => {
            this['$message'].error(e)
          })
      },
    }
  }

  mounted() {
    tinymce.init({})
    this.value = this.content
  }

  onEditorInput(e: any) {
    this.$emit('input', {
      key: this.id,
      value: this.value
    })
  }

  setContent(str: any) {
    this.value = str
  }
}
</script>

<style lang="scss" scoped></style>
<style>
.tox-tinymce-aux {
  z-index: 9999 !important;
}
</style>