<template>
  <el-select v-model="itemForm.questionType" @change="handleClick" placeholder="请选择.." disabled>
    <el-option
      v-for="item in buttonGroup"
      :key="item.type"
      :label="item.name"
      :value="item.type"
      :disabled="!item.status"
    ></el-option>
  </el-select>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { buttonGroup } from "./../../assets/javascript/util";
import { ajaxNew } from "./../../api/index";
import { Mutation } from "vuex-class";

@Component({
  name: "ChangeType"
})
class ChangeType extends Vue {
  @Prop() private propParams;
  @Mutation("setQsListRefresh") setQsListRefresh;

  private flag = true;
  private itemForm = this.propParams;
  private buttonGroup = buttonGroup;

  handleClick(type: number) {
    this["$confirm"](`转换成将丢失所有选项信息，是否继续？`)
      .then(() => {
        this.handleChange(type);
      })
      .catch(e => {
        console.log(e);
      });
  }
  handleChange(type: number) {
    this.itemForm.questionType = type;
    ajaxNew("/business/api.v1.question/qsubject/mod", this.itemForm, "post")
      .then(res => {
        if (res.resultCode === "0") {
          this.setQsListRefresh(true);
        } else {
          this["$message"].error(res.resultMsg);
        }
      })
      .catch(e => {
        this["$message"].error(e);
      });
  }

  mounted() {
    // console.log("来自radio组件的值", this.propParams);
  }
}
export default ChangeType;
</script>