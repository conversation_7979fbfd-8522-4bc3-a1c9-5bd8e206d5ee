<template>
  <el-form ref="form" :model="formModel" label-width="100px" style="width: 100%;">
    <el-form-item :label="itemRelevance.source === 'card' ? '当前磁块' : '当前题目'">
      {{itemRelevance.sortShow}}.
      {{ itemRelevance.questionTitle }}
    </el-form-item>
    <el-table :data="tables" style="width: 100%" border>
      <el-table-column :label="itemRelevance.source === 'card' ? '当前项目' : '当前选项'" width="200">
        <template slot-scope="scope">{{ scope.row.optionInfo }}</template>
      </el-table-column>
      <el-table-column :label="itemRelevance.source === 'card' ? '项目出现条件' : '选项出现条件'">
        <template slot-scope="scope">
          <div v-if="!scope.row.isEdit" class="item_relevance_row">
            <div v-if="scope.row.qsTooltip">{{scope.row.qsTooltip}}</div>
            <div v-else></div>
            <el-button @click="handleAdd(scope.row, scope.$index)" v-if="scope.row.qsTooltip">修改关联</el-button>
            <el-button @click="handleAdd(scope.row, scope.$index)" v-if="!scope.row.qsTooltip">添加关联</el-button>
          </div>
          <qsRelevance
            :qsRelevanceParam="itemRelevance"
            @childSendQs="childSendQsfun"
            v-if="scope.row.isEdit"
          />
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import QsRelevance from "./QsRelevance.vue";
import { Mutation } from "vuex-class";

@Component({
  name: "ItemRelevance",
  components: {
    QsRelevance,
  },
})
class ItemRelevance extends Vue {
  @Prop() private itemRelevanceParam;
  @Mutation("setQsListRefresh") setQsListRefresh;
  @Watch("itemRelevanceParam", { immediate: true, deep: true })
  getItemRelevanceParam(newVal) {
    if (newVal) this.itemRelevance = newVal;
    this.tables = [];
    // fix bug TIC-CENTER-WEB-3
    newVal.options && newVal.options.length && newVal.options.forEach((pItem) => {
      // 关联关系文本显示
      const relateArray = [];
      if (pItem.relateItem && pItem.relateItem.length) {
        pItem.relateItem.forEach((item) => {
          let condition = "";
          if (item.optionInfo.includes(",") && item.isUnion) {
            condition = "中的全部选项时";
          } else if (item.optionInfo.includes(",") && !item.isUnion) {
            condition = "中的其中一个选项时";
          } else {
            condition = "时";
          }
          relateArray.push(
            `当题目${item.questionTitle}，${
              item.isForward ? "选择" : "没有选择"
            }[${item.optionInfo}]${condition}`
          );
          let unionType = "";
          if (relateArray.length > 1) {
            unionType = item.unionType ? " 且 " : " 或 ";
          }
          pItem.qsTooltip = relateArray.join(unionType) + "此选项才显示";
        });
      }

      // 过滤父级包含了子集的选项。
      if (pItem.optionItem) {
        if (!pItem.optionItem.length) {
          this.tables.push(pItem);
        } else {
          this.tables = this.tables.concat(pItem.optionItem);
        }
      }
      if (newVal.source === "card") {
        this.tables = newVal.options;
      }
    });
    this.resetOptionItem();
  }

  private itemRelevance;
  private formModel = {};
  private tables = [];

  // 添加点击
  handleAdd(item, index: number): void {
    this.tables.forEach((item) => {
      item.isEdit = false;
    });
    item.isEdit = true;
    this.itemRelevance.source = this.itemRelevance.source || "option"; // 设置来源，标识来自问题的关联
    this.itemRelevance.optionIndex = index;
    this.itemRelevance.optionNo = item.optionNo; // 添加选项no
    this.resetOptionItem();
  }

  // 接收子组件的索引，关闭编辑框
  childSendQsfun(res): void {
    if (res) {
      this.itemRelevance.options[res.optionIndex].isEdit = false;
      this.tables.forEach((item) => {
        item.isEdit = false;
      });
      this.setQsListRefresh({isRresh: true});
    }
    this.resetOptionItem();
  }

  // 强制刷新数据
  resetOptionItem(): void {
    this.itemRelevance = JSON.parse(JSON.stringify(this.itemRelevance));
    this.tables = JSON.parse(JSON.stringify(this.tables));
  }
}
export default ItemRelevance;
</script>

<style lang="less" scoped>
.item_relevance_row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
</style>>