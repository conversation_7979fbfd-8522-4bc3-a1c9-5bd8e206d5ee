<template>
  <el-row>
    <el-col :span="24">
      <el-form ref="form"
               :model="itemForm"
               label-width="100px">
        <el-form-item :label="itemForm.sortShow + '.'">
          <span v-if="itemForm.isMust"
                class="must">*</span>
          {{ itemForm.questionTitle || '标题' }}
        </el-form-item>
        <el-form-item v-if="itemForm.memo">{{ itemForm.memo }}</el-form-item>
        <el-form-item>
          <el-upload class="upload-demo"
                     action>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">{{ '图片识别' }}</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="24"
            v-if="itemForm.isEdit"
            class="question_eidt">
      <el-form ref="form"
               :model="itemForm"
               label-width="100px">
        <el-form-item label="问题标题">
          <el-input v-model="itemForm.questionTitle"
                    maxlength="300"
                    placeholder="请输入问题标题..."></el-input>
        </el-form-item>
        <el-form-item label="题目说明">
          <el-input v-model="itemForm.memo"
                    maxlength="250"
                    placeholder="请输入题目说明..."
                    type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="上传文件个数">
          <el-select style="width: 300px; margin-right: 20px;"
                     v-model="itemForm.optionMemo">
            <el-option v-for="item of 9"
                       :label="item"
                       :key="item"
                       :value="item"></el-option>
          </el-select>
          <span>最多9张</span>
        </el-form-item>
        <el-form-item label="上传文件类型">
          <el-radio-group v-model="itemForm.uploadType">
            <el-radio :label="3">图片</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选项标签">
          <el-select style="width: 300px; margin-right: 20px;"
                     v-model="itemForm.labelCode">
            <el-option v-for="(item, index) of formatLabels"
                       :label="item.labelName"
                       :key="index"
                       :value="item.labelCode"></el-option>
          </el-select>
          <el-checkbox v-model="itemForm.isMust"
                       style="margin-left: 20px;"
                       :true-label="1"
                       :false-label="0">必填</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="modQuestion">完成编辑</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'

@Component({
  name: 'UploadAI',
  components: {},
})
class Upload extends Vue {
  // 父组件传进来的值
  @Prop() private childParams
  // 监控数据变化
  //immediate为true的时候，会刚开始时就执行一次，比如第一次渲染该页面
  //deep为true时，代表同时监听对象内部属性情况，内部变化，也会触发该函数
  @Watch('childParams', { immediate: true, deep: true })
  getChildParams(newVal) {
    if (newVal && newVal.isEdit) {
      // 创建一个新对象而不是直接引用
      this.itemForm = JSON.parse(JSON.stringify(newVal))
    }
  }
  @Prop() private labels
  @Watch('labels', { immediate: true, deep: true })
  getLabels(newVal) {
    if (newVal) {
      this.formatLabels = newVal.filter((v: any) => v.labelCode !== 'category')
    }
  }
  // 编辑参数
  private itemForm = {} as any
  private formatLabels = [] as any
  // 编辑题目
  modQuestion(): void {
    this.itemForm.isEdit = false
    // 将修改后的值返回给父组件
    this.$emit('modQuestion', this.itemForm)
  }
  mounted(): void {}
}
export default Upload
</script>

<style lang="less" scoped>
.must {
  color: #f00;
}
.upload-demo {
  border: 1px dashed #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;

  .el-icon-upload {
    font-size: 50px;
    color: #409eff;
  }
}
</style>
