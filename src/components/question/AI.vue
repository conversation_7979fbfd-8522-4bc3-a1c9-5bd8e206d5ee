<template>
  <el-row>
    <el-col :span="24">
      <el-form ref="form"
               :model="itemForm"
               label-width="100px">
        <el-form-item :label="itemForm.sortShow + '.'">
          <span v-if="itemForm.isMust"
                class="must">*</span>
          {{ itemForm.questionTitle || '标题' }}
        </el-form-item>
        <el-form-item v-if="itemForm.memo">{{ itemForm.memo }}</el-form-item>
      </el-form>
    </el-col>
    <el-col :span="24"
            v-if="itemForm.isEdit"
            class="question_eidt">
      <el-form ref="form"
               :model="itemForm"
               label-width="100px">
        <el-form-item label="问题标题">
          <el-input v-model="itemForm.questionTitle"
                    maxlength="300"
                    placeholder="请输入问题标题..."
                    disabled></el-input>
        </el-form-item>
        <el-form-item label="大模型">
          <el-radio-group v-model="itemForm.aiModel">
            <el-radio label="GPT-4o"
                      value="GPT-4o">GPT-4o</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="*字段">
          <div class="item-title">
            <div class="item-optionInfo">提示词</div>
            <div class="item-recommend">选项标签</div>
            <div class="item-action">操作</div>
          </div>
          <div class="item-list"
               v-for="(parentItem, parentIndex) of itemForm.options"
               :key="parentIndex">
            <div class="item-parent">
              <div class="item-optionInfo">
                <el-input placeholder="请输入提示词..."
                          maxlength="500"
                          v-model="parentItem.optionInfo"
                          type="textarea"></el-input>
              </div>
              <div class="item-recommend">
                <el-select v-model="parentItem.labelCode">
                  <el-option v-for="(item, index) of labels"
                             :label="item.labelName"
                             :key="index"
                             :value="item.labelCode"
                             :disabled="disabledOption.includes(item.labelCode)"></el-option>
                </el-select>
              </div>
              <div class="item-action">
                <el-button-group>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-minus"
                             @click="handleDel(parentIndex)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-plus"
                             @click="handleAdd"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-top"
                             :disabled="!parentIndex"
                             @click="handleMove(parentIndex, 'up')"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-bottom"
                             :disabled="parentIndex == itemForm.options.length - 1"
                             @click="handleMove(parentIndex, 'down')"></el-button>
                </el-button-group>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="modQuestion">完成编辑</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { generateRandomString } from './../../assets/javascript/util'

@Component({
  name: 'AI',
  components: {},
})
class AI extends Vue {
  @Prop() private labels
  @Watch('labels', { immediate: true, deep: true })
  getLabels(newVal) {
    if (newVal) {
    }
  }
  // 父组件传进来的值
  @Prop() private childParams
  // 监控数据变化
  //immediate为true的时候，会刚开始时就执行一次，比如第一次渲染该页面
  //deep为true时，代表同时监听对象内部属性情况，内部变化，也会触发该函数
  @Watch('childParams', { immediate: true, deep: true })
  getChildParams(newVal) {
    if (newVal && newVal.isEdit) {
      if (newVal.options && !newVal.options.length)
        newVal.options.push(this.option)
      // 创建一个新对象而不是直接引用
      this.itemForm = JSON.parse(JSON.stringify(newVal))
    }
  }
  private itemForm = {} as any
  // 单个选项
  private option = {
    subjectNo: '',
    optionNo: generateRandomString(16),
    optionInfo: '',
    sortShow: 0,
    labelCode: '',
  } as any

  /* 
    监听答案选项的变化
  */
  private disabledOption = [] as any // 禁用的选项
  watchChangeOption(): void {
    console.log(this.itemForm.options, 'this.itemForm.options')
    this.disabledOption = this.itemForm.options
      .map((option) => option.labelCode)
      .filter((code) => code) // 过滤掉空值
  }
  @Watch('itemForm.options', { deep: true })
  onOptionsChange() {
    this.watchChangeOption()
  }

  handleDel(parentIndex: number): void {
    // 删除选项， 仅有一条禁止删除
    if (this.itemForm.options.length === 1) {
      this.$message({
        type: 'warning',
        message: '至少需要一个选项',
      })
      return
    }

    this.$confirm(`您确认删除吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        // 删除itemForm.options中的选项
        this.itemForm.options.splice(parentIndex, 1)
      })
      .catch(() => {})
  }

  handleAdd(): void {
    // 每次随机生成一个选项值
    this.option.optionNo = generateRandomString(16)
    this.itemForm.options.push(this.option)
    this.itemForm = JSON.parse(JSON.stringify(this.itemForm))
    this.$emit('modQuestion', this.itemForm)
  }
  handleMove(parentIndex: number, arrow: string): void {
    const list = this.itemForm.options
    if (arrow === 'up' && parentIndex > 0) {
      // 上移：交换当前项和上一项
      ;[list[parentIndex - 1], list[parentIndex]] = [
        list[parentIndex],
        list[parentIndex - 1],
      ]
    }
    if (arrow === 'down' && parentIndex < list.length - 1) {
      // 下移：交换当前项和下一项
      ;[list[parentIndex], list[parentIndex + 1]] = [
        list[parentIndex + 1],
        list[parentIndex],
      ]
    }
    this.itemForm.options = JSON.parse(JSON.stringify(list))
  }

  mounted(): void {
    this.onOptionsChange()
  }
  // 编辑题目
  modQuestion(): void {
    // 校验 options 下每个 optionInfo 和 labelCode 是否有值
    const invalidOption = this.itemForm.options.some(
      (option) => !option.optionInfo.trim() || !option.labelCode
    )
    if (invalidOption) {
      this.$message({
        type: 'warning',
        message: '每个字段的提示词和标签都不能为空',
      })
      return
    }
    this.itemForm.isEdit = false
    // 将修改后的值返回给父组件
    this.$emit('modQuestion', this.itemForm)
  }
}
export default AI
</script>

<style>
.el-table .cell {
  display: flex;
  align-items: center;
}
</style>

<style lang="less" scoped>
.item-recommend {
  width: 200px !important;
}
</style>