<template>
  <el-row v-loading='loading'>
    <el-col :span="24">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item :label="itemForm.sortShow + '.'">
          <span v-if="itemForm.isMust"
                class="must">*</span>
          {{ itemForm.questionTitle || '标题' }}
          <img style="width: 20px; height: 20px; margin: 0 3px; cursor: pointer;"
               v-if="itemForm.imgPath"
               :src="itemForm.imgPath"
               @click='uploadFileDialog = true' />
          <span style="color: #ccc;">[下拉+填空题]</span>
        </el-form-item>
        <el-form-item v-if="itemForm.memo">{{ itemForm.memo }}</el-form-item>
        <el-form-item v-if="qsTooltip">{{ qsTooltip }}</el-form-item>
        <el-form-item>
          <el-input disabled
                    type="textarea"
                    :rows="10 / itemForm.textSize"
                    :placeholder="itemForm.fillMemo"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="isDefualt"
                     :placeholder="itemForm.optionMemo">
            <el-option v-for='(item, index) of itemForm.optionItem'
                       :key='index'
                       :label="item.optionInfo"
                       :value="item.optionId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="24"
            v-if="childParams.isEdit"
            class="question_eidt">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item label="问题标题">
          <el-input maxlength="300"
                    v-model="itemForm.questionTitle"
                    placeholder="请输入问题标题..."
                    style="width: 300px; margin-right: 15px;"></el-input>
          <el-checkbox style="margin-left: 50px;"
                       v-model="itemForm.isMust"
                       :true-label="1"
                       :false-label="0">PC必填</el-checkbox>
          <el-checkbox v-model="itemForm.isSimplifiedMode"
                       :true-label="1"
                       :false-label="0"
                       @change="handleIsSimplifiedMode">移动端展示</el-checkbox>
          <el-checkbox v-model="itemForm.isSimplifiedMustFill"
                       :true-label="1"
                       :false-label="0"
                       v-if="itemForm.isSimplifiedMode">移动端必填</el-checkbox>
          <el-button type="text"
                     style="float: right;"
                     @click="handleRelateItem">
            关联推荐项目
            <span v-if="itemForm.relateId">({{ itemForm.relateId }})</span>
          </el-button>
        </el-form-item>
        <el-form-item label='问题选项'>
          <div class="item-title">
            <div class="item-optionInfo">选项文字</div>
            <div class="item-action">操作</div>
          </div>
          <div class="item-list"
               v-for="(parentItem, parentIndex) of itemForm.optionItem"
               :key="parentIndex">
            <div class="item-parent">
              <div class="item-optionInfo">
                <el-input placeholder="请输入文字..."
                          maxlength="10"
                          v-model="parentItem.optionInfo"></el-input>
              </div>
              <div class="item-action">
                <el-button-group>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-minus"
                             @click="handleDel(parentIndex)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-plus"
                             @click="handleAdd(parentIndex)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-top"
                             :disabled="!parentIndex"
                             @click="handleMove(parentIndex, 'up')"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-bottom"
                             :disabled="parentIndex == itemForm.optionItem.length - 1"
                             @click="handleMove(parentIndex, 'down')"></el-button>
                </el-button-group>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label='选项默认文字'>
          <el-input placeholder="请输入选项默认文字"
                    maxlength="8"
                    v-model="itemForm.optionMemo"></el-input>
        </el-form-item>
        <el-form-item label>
          <el-button type="text"
                     @click="handleShowMome('memo')">题目说明</el-button>
          <el-button type="text"
                     @click="uploadFileDialog = true">上传图片</el-button>
          <el-button type="text"
                     @click="handleShowMome('fillMemo')">编辑说明</el-button>
        </el-form-item>
        <el-form-item label>
          <el-tooltip class="item"
                      effect="dark"
                      :content="qsTooltip"
                      placement="top-end">
            <el-button type="text"
                       @click="handleQsRelevance">问题关联设置</el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="modQuestion">完成编辑</el-button>
        </el-form-item>
      </el-form>
    </el-col>
    <el-dialog :title="'请填写' + momeDialog.title"
               :visible.sync="momeDialog.visible"
               width="30%">
      <el-input maxlength="250"
                type="textarea"
                :rows="3"
                v-model="momeDialog.value"
                :placeholder="'请输入'+ momeDialog.title"></el-input>
      <div style="margin-top: 10px;"
           v-if='momeDialog.type === "memo"'>
        <el-checkbox v-model="momeDialog.isShowMemo"
                     :true-label="1"
                     :false-label="0">展示到页面</el-checkbox>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="momeDialog.visible = false">取 消</el-button>
        <el-button type="primary"
                   @click="handleModal">确 认</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源上传"
               :visible.sync="uploadFileDialog"
               width="500px">
      <FileUpload :imgPath="itemForm"
                  @childSend="childSendfun" />
      <el-button @click="clearImg"
                 type="primary">删除图片</el-button>
    </el-dialog>
    <el-dialog title="题目关联"
               :visible.sync="qsRelevanceDialog"
               width="50%">
      <qsRelevance :qsRelevanceParam="qsRelevanceParam"
                   @childSendQs="childSendQsfun" />
    </el-dialog>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from '../../api/index'
import FileUpload from '../FileUpload.vue'
import QsRelevance from './QsRelevance.vue'

@Component({
  name: 'InputSelect',
  components: {
    FileUpload,
    QsRelevance,
  },
})
class InputSelect extends Vue {
  @Mutation('setQsListRefresh') setQsListRefresh
  // 父组件传进来的值
  @Prop() private childParams

  // 监控数据变化
  //immediate为true的时候，会刚开始时就执行一次，比如第一次渲染该页面
  //deep为true时，代表同时监听对象内部属性情况，内部变化，也会触发该函数
  @Watch('childParams', { immediate: true, deep: true })
  getChildParams(newVal) {
    if (newVal) {
      this.qsRelevanceParam = newVal

      if (newVal.relateItem && newVal.relateItem.length) {
        const relateArray = []
        newVal.relateItem.forEach((item) => {
          // 当题目1. 题目1选择[选项1]时，此题才显示
          // 当题目1. 题目1选择[选项1]时，当题目2.题目2选择[选项4]时，此题才显示
          // 当题目1. 题目1选择[选项1]时，当题目2.题目2选择[选项4]、[选项5]中的全部选项时，此题才显示
          // 当题目1. 题目1选择[选项1]时，当题目2.题目2选择[选项4]、[选项5]中的其中一个选项时，此题才显示
          let condition = ''
          if (item.optionInfo.includes(',') && item.isUnion) {
            condition = '中的全部选项时'
          } else if (item.optionInfo.includes(',') && !item.isUnion) {
            condition = '中的其中一个选项时'
          } else {
            condition = '时'
          }
          relateArray.push(
            `当题目${item.questionTitle}，${
              item.isForward ? '选择' : '没有选择'
            }[${item.optionInfo}]${condition}`
          )
        })
        let unionType = ''
        if (relateArray.length > 1) {
          unionType = newVal.relateItem[0].unionType ? ' 且 ' : ' 或 '
        }
        this.qsTooltip = relateArray.join(unionType) + '此题才显示'
      } else {
        this.qsTooltip = ''
      }
      newVal.optionItem = newVal.options
      const thisitem = JSON.parse(JSON.stringify(this.item))
      if (!newVal.optionItem || !newVal.optionItem.length) {
        newVal.optionItem = [thisitem]
      }
      this.itemForm = newVal
      this.itemForm = JSON.parse(JSON.stringify(this.itemForm))
      // 获取测试项目数量
      const questionId = this['$route'].query.id as string
      if (newVal.isEdit) this.getTextItemNum(newVal.subjectNo, questionId)
    }
  }
  private isDefualt = ''
  private formModel = {}

  // 上传文件弹窗
  private uploadFileDialog = false

  // 编辑参数
  private itemForm = {
    partNo: this.childParams.partNo,
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    questionType: this.childParams.questionType,
    questionTitle: this.childParams.questionTitle || '问题标题',
    isMust: this.childParams.isMust || 0,
    isSimplifiedMode: this.childParams.isSimplifiedMode || 0,
    isSimplifiedMustFill: this.childParams.isSimplifiedMustFill || 0,
    isHot: this.childParams.isHot || 0,
    imgPath: this.childParams.imgPath,
    textSize: this.childParams.textSize || 3,
    uploadType: this.childParams.uploadType || 1,
    sortShow: this.childParams.sortShow,
    memo: this.childParams.memo,
    fillMemo: this.childParams.fillMemo,
    optionMemo: this.childParams.optionMemo,
    optionItem: this.childParams.optionItem || [],
    isEdit: this.childParams.isEdit,
    relateId: 0,
    options: [],
    relateItem: [],
    isShowMemo: this.childParams.isShowMemo,
  }

  // 单个问题项
  private item = {
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    optionInfo: '',
    imgPath: '',
    groupImage: '',
    memo: '',
    fillNotice: '',
    isFill: 0,
    isMust: 0,
    relateType: 0,
    isDefualt: 0,
    isGroup: 0,
    isDisabled: false,
    sortShow: 0,
    isShowMemo: 0,
  }

  // 说明弹窗
  private momeDialog = {
    type: '',
    title: '',
    value: '',
    visible: false,
    isShowMemo: 0,
  }

  // 题目关联弹窗
  private qsRelevanceDialog = false
  private qsRelevanceParam = {}
  private qsTooltip = ''
  private loading = false

   // 取消必填
   handleIsSimplifiedMode(val: number): void {
    if (!val) this.itemForm.isSimplifiedMustFill = 0
  }
  // 关联测试项目
  handleRelateItem(): void {
    const subjectNo = this.itemForm.subjectNo
    const questionId = this['$route'].query.id
    window.open(`testItem?questionId=${questionId}&subjectNo=${subjectNo}`)
  }

  // 接收子组件的返回值
  childSendfun(res: string) {
    if (res) this.itemForm.imgPath = res
  }

  childSendQsfun(res): void {
    if (res) {
      this.qsRelevanceDialog = false
    }
  }

  getTextItemNum(subjectNo, questionId): void {
    ajaxNew(
      '/business/api.v1.question/qitem/qrySum',
      {
        subjectNo,
        questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const relList = res.data.items
          this.itemForm.relateId = relList.length ? relList[0].relateId : 0
          this.itemForm = JSON.parse(JSON.stringify(this.itemForm))
          console.log(this.itemForm)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 显示说明添加
  handleShowMome(type: string): void {
    const thisItem = JSON.parse(JSON.stringify(this.itemForm))
    if (type === 'memo') {
      this.momeDialog.title = '题目说明'
      this.momeDialog.value = thisItem.memo
      this.momeDialog.isShowMemo = thisItem.isShowMemo
    } else {
      this.momeDialog.title = '编辑说明'
      this.momeDialog.value = thisItem.fillMemo
    }
    this.momeDialog.type = type
    this.momeDialog.visible = true
  }

  // 编辑题目
  modQuestion(): void {
    // 上传前去除多余的参数，并添加sortShow
    const itemFormClone = JSON.parse(JSON.stringify(this.itemForm))
    if (itemFormClone.optionItem.length) {
      itemFormClone.optionItem.map((v, i) => {
        return (v.sortShow = i)
      })
    }
    this.itemForm = itemFormClone
    delete this.itemForm.isEdit
    delete this.itemForm.options
    delete this.itemForm.relateItem
    if (!this.itemForm.questionTitle) {
      this['$message'].error('问题标题不能为空')
    } else {
      let flag = 0
      this.itemForm.optionItem.filter((item) => {
        if (!item.optionInfo) flag++
        if (item.optionItem && item.optionItem.length) {
          item.optionItem.forEach((cItem) => {
            if (!cItem.optionInfo) flag++
          })
        }
      })
      if (flag) {
        this['$message'].error('选项标题不能为空')
      } else {
        ajaxNew('/business/api.v1.question/qsubject/mod', this.itemForm, 'post')
          .then((res) => {
            if (res.resultCode === '0') {
              this.childParams.isEdit = false
              this.setQsListRefresh(true)
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            this['$message'].error(e)
          })
      }
    }
  }

  // 子选项的相关操作
  handleDel(parentIndex: number, childrenIndex?: number): void {
    if (childrenIndex === undefined) {
      if (this.itemForm.optionItem.length === 1) {
        this['$message'].error('已经是最后一条了,不能在删除.')
      } else {
        this['$confirm'](`您确认删除？`)
          .then(() => {
            this.delOption(this.itemForm.optionItem[parentIndex])
            this.itemForm.optionItem.splice(parentIndex, 1)
            this.resetOptionItem()
          })
          .catch(() => {
            // this["$message"].error(e);
          })
      }
    } else {
      this['$confirm'](`您确认删除？`)
        .then(() => {
          this.delOption(
            this.itemForm.optionItem[parentIndex].optionItem[childrenIndex]
          )
          this.itemForm.optionItem[parentIndex].optionItem.splice(
            childrenIndex,
            1
          )
          this.resetOptionItem()
        })
        .catch(() => {
          // this["$message"].error(e);
        })
    }
  }
  handleAdd(parentIndex: number, childIndex?: number): void {
    const item = JSON.parse(JSON.stringify(this.item))
    // 插入在当前点击按钮的下一个
    if (childIndex === undefined) {
      if (this.itemForm.optionItem.length < 15) {
        item.optionInfo = `选项名称${this.itemForm.optionItem.length}`
        this.itemForm.optionItem.splice(parentIndex + 1, 0, item)
      } else {
        this['$message'].error('下拉选项最多15个')
      }
    } else {
      if (!this.itemForm.optionItem[parentIndex].relateId) {
        item.optionInfo = `子选项-${this.itemForm.optionItem[parentIndex].optionItem.length}`
        this.itemForm.optionItem[parentIndex].optionItem.splice(
          childIndex + 1,
          0,
          item
        )
      } else {
        this['$message'].error('选项已关联推荐项目，请删除后再添加子选项')
      }
    }
    this.resetOptionItem()
  }
  handleMove(parentIndx: number, arrow: string, childIndex?: number): void {
    if (childIndex === undefined) {
      if (arrow === 'up') {
        this.itemForm.optionItem[parentIndx] = this.itemForm.optionItem.splice(
          parentIndx - 1,
          1,
          this.itemForm.optionItem[parentIndx]
        )[0]
      } else if (arrow === 'down') {
        this.itemForm.optionItem[parentIndx] = this.itemForm.optionItem.splice(
          parentIndx + 1,
          1,
          this.itemForm.optionItem[parentIndx]
        )[0]
      }
    } else {
      if (arrow === 'up') {
        this.itemForm.optionItem[parentIndx].optionItem[childIndex] =
          this.itemForm.optionItem[parentIndx].optionItem.splice(
            childIndex - 1,
            1,
            this.itemForm.optionItem[parentIndx].optionItem[childIndex]
          )[0]
      } else if (arrow === 'down') {
        this.itemForm.optionItem[parentIndx].optionItem[childIndex] =
          this.itemForm.optionItem[parentIndx].optionItem.splice(
            childIndex + 1,
            1,
            this.itemForm.optionItem[parentIndx].optionItem[childIndex]
          )[0]
      }
    }
    this.resetOptionItem()
  }
  // 插入子选项
  handleInserChildItem(parentIndex: number): void {
    if (!this.itemForm.optionItem[parentIndex].relateId) {
      const item = JSON.parse(JSON.stringify(this.item))
      item.optionInfo = `子选项-${this.itemForm.optionItem[parentIndex].optionItem.length}`
      this.itemForm.optionItem[parentIndex].optionItem.push(item)
      this.resetOptionItem()
    } else {
      this['$message'].error('选项已关联推荐项目，请删除后再添加子选项')
    }
  }
  // 删除选项
  delOption(option): void {
    if (option.subjectNo && option.optionNo) {
      this.loading = true
      ajaxNew(
        '/business/api.v1.question/qoption/del',
        {
          partNo: this.itemForm.partNo,
          subjectNo: option.subjectNo,
          optionNo: option.optionNo,
        },
        'post'
      )
        .then((res) => {
          if (res.resultCode === '0') {
            // this.childParams.isEdit = false;
            this.resetOptionItem()
            this.loading = false
          } else {
            this['$message'].error(res.resultMsg)
            this.loading = false
          }
        })
        .catch((e) => {
          this['$message'].error(e)
          this.loading = false
        })
    }
  }
  // 强制刷新数据
  resetOptionItem(): void {
    this.setGroupStatus()
    this.itemForm = JSON.parse(JSON.stringify(this.itemForm))
  }
  // 动态控制父级按钮状态
  setGroupStatus(): void {
    this.itemForm.optionItem.forEach((pItem) => {
      if (pItem.optionItem && pItem.optionItem.length) {
        pItem.isFill = 0
        pItem.isMust = 0
        pItem.isDefualt = 0
        pItem.isGroup = 0
        pItem.isDisabled = true
      } else {
        pItem.isDisabled = false
      }
    })
  }
  // 确认说明按点击
  handleModal() {
    this.momeDialog.visible = false
    if (this.momeDialog.type === 'memo') {
      this.itemForm.memo = this.momeDialog.value
      this.itemForm.isShowMemo = this.momeDialog.isShowMemo
    } else {
      this.itemForm.fillMemo = this.momeDialog.value
    }
  }
  // 点击问题关联
  handleQsRelevance() {
    this.qsRelevanceDialog = true
  }
  // 删除图片
  clearImg() {
    this.itemForm.imgPath = ''
  }

  mounted(): void {}
}
export default InputSelect
</script>

<style lang="less" scoped>
.must {
  color: #f00;
}
.upload-demo {
  border: 1px dashed #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;

  .el-icon-upload {
    font-size: 50px;
    color: #409eff;
  }
}
</style>