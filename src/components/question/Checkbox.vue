<template>
  <el-row v-loading='loading'>
    <el-col :span="24">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item :label="itemForm.sortShow + '.'">
          <span v-if="itemForm.isMust"
                class="must">*</span>
          {{ itemForm.questionTitle || '标题' }}
          <img style="width: 20px; height: 20px; margin: 0 3px; cursor: pointer;"
               v-if="itemForm.imgPath"
               :src="itemForm.imgPath"
               @click='handleUplodaImgSubject' />
          <span style="color: #ccc;">[{{ itemForm.questionType === 2 ? '单选题(平铺)' : itemForm.questionType === 3 ? '多选题（平铺）' : itemForm.questionType === 4 ? '单选题（下拉）' : '多选题（下拉）' }}]</span>
          <b style="padding-left: 10px;"
             v-if='itemForm.isHide === "1"'>隐藏题</b>
        </el-form-item>
        <el-form-item v-if="itemForm.memo">{{ itemForm.memo }}</el-form-item>
        <el-form-item v-if="qsTooltip">{{ qsTooltip }}</el-form-item>
        <el-form-item v-if="itemForm.optionItem.length && (itemForm.questionType === 2 || itemForm.questionType === 3)">
          <div v-for="(item, index) of itemForm.optionItem"
               :key="index">
            <el-radio disabled
                      :label="String(item.isDefualt)"
                      :key="index"
                      v-model="isDefualt">{{item.optionInfo}}</el-radio>
            <div v-if="item.optionItem.length"
                 style="margin-left: 20px;">
              <el-radio-group v-model="isDefualt"
                              disabled>
                <el-radio v-for="(childrenItem, childdrenIndex) of item.optionItem"
                          :label="String(childrenItem.isDefualt)"
                          :key="childdrenIndex">{{childrenItem.optionInfo}}</el-radio>
              </el-radio-group>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="itemForm.optionItem.length && (itemForm.questionType === 4 || itemForm.questionType === 5)">
          <el-select style="width: 100%;"
                     v-model="isDefualt"
                     v-if="itemForm.questionType === 4">
            <el-option v-for="(item, index) of itemForm.optionItem"
                       :label="item.optionInfo"
                       :key="index"
                       :value="String(item.isDefualt)"></el-option>
          </el-select>
          <el-select style="width: 100%;"
                     v-model="isDefualtArr"
                     v-if="itemForm.questionType === 5"
                     multiple>
            <el-option v-for="(item, index) of itemForm.optionItem"
                       :label="item.optionInfo"
                       :key="index"
                       :value="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="24"
            v-if="childParams.isEdit"
            class="question_eidt">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item label="问题标题">
          <el-input maxlength="300"
                    v-model="itemForm.questionTitle"
                    placeholder="请输入问题标题..."
                    style="width: 300px; margin-right: 15px;"></el-input>
          <el-checkbox v-model="itemForm.isMust"
                       :true-label="1"
                       :false-label="0">PC必填</el-checkbox>
          <el-checkbox v-model="itemForm.isSimplifiedMode"
                       :true-label="1"
                       :false-label="0"
                       @change="handleIsSimplifiedMode">移动端展示</el-checkbox>
          <el-checkbox v-model="itemForm.isSimplifiedMustFill"
                       :true-label="1"
                       :false-label="0"
                       v-if="itemForm.isSimplifiedMode">移动端必填</el-checkbox>
        </el-form-item>
        <el-form-item label="展示样式">
          <el-select v-model="itemForm.isShowImg">
            <el-option value="0"
                       label="无图模式">无图模式</el-option>
            <el-option value="1"
                       label="有图模式">有图模式</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否展示">
          <el-select v-model="itemForm.isHide">
            <el-option value="0"
                       label="展示到用户端">展示到用户端</el-option>
            <el-option value="1"
                       label="对用户不可见">对用户不可见</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签配置">
          <el-select style="width: 300px; margin-right: 20px;"
                     v-model="itemForm.labelCode">
            <el-option v-for="(item, index) of labels"
                       :label="item.labelName"
                       :key="index"
                       :value="item.labelCode"></el-option>
          </el-select>
          <!-- <span style="padding-left: 20px;">用来关联AI识别结果</span> -->
        </el-form-item>
        <el-form-item label="问题选项">
          <div class="item-title">
            <div class="item-optionInfo">选项文字</div>
            <div class="item-imgPath"
                 v-if="itemForm.questionType === 2 || itemForm.questionType === 3">图片</div>
            <div class="item-memo"
                 v-if="itemForm.questionType === 2 || itemForm.questionType === 3">说明</div>
            <div class="item-isFill"
                 v-if="itemForm.questionType !== 5">允许填空</div>
            <div class="item-isDefualt">默认选择</div>
            <div class="item-relate"
                 v-if="itemForm.questionType === 3">互斥</div>
            <div class="item-group">关联问题分组</div>
            <div class="item-recommend">关联项目</div>
            <div class="item-action">操作</div>
          </div>
          <div class="item-list"
               v-for="(parentItem, parentIndex) of itemForm.optionItem"
               :key="parentIndex">
            <div class="item-parent">
              <div class="item-optionInfo">
                <el-input placeholder="请输入文字..."
                          maxlength="100"
                          v-model="parentItem.optionInfo"
                          type="textarea"></el-input>
                <el-button v-if="itemForm.questionType === 2 || itemForm.questionType === 3"
                           icon="el-icon-plus"
                           type="text"
                           style="margin-right: 10px;"
                           @click="handleInserChildItem(parentIndex)">插入子选项</el-button>
              </div>
              <div class="item-imgPath"
                   v-if="itemForm.questionType === 2 || itemForm.questionType === 3">
                <template>
                  <i class="el-icon-document"
                     @click="handleUplodaImg(parentIndex)"
                     v-if="!parentItem.imgPath"></i>
                  <img :src="parentItem.imgPath"
                       @click="handleUplodaImg(parentIndex)"
                       v-else />
                </template>
              </div>
              <div class="item-memo"
                   v-if="itemForm.questionType === 2 || itemForm.questionType === 3">
                <template>
                  <i class="el-icon-document"
                     @click="handleAddMemo(parentIndex, undefined, 'memo')"
                     v-if="!parentItem.memo"></i>
                  <i class="el-icon-document"
                     @click="handleAddMemo(parentIndex, undefined, 'memo')"
                     style="color: #409EFF;"
                     v-else></i>
                </template>
              </div>
              <div class="item-isFill"
                   v-if="itemForm.questionType !== 5">
                <template>
                  <el-checkbox :disabled="parentItem.isDisabled"
                               v-model="parentItem.isFill"
                               :true-label="1"
                               :false-label="0"
                               @change="handleChangeIsFull(parentIndex)"></el-checkbox>
                  <el-checkbox :disabled="parentItem.isDisabled"
                               v-model="parentItem.isMust"
                               :true-label="1"
                               :false-label="0"
                               v-if="parentItem.isFill">是否必填</el-checkbox>
                  <template v-if="parentItem.isFill">
                    <i class="el-icon-document"
                       @click="handleAddMemo(parentIndex, undefined, 'fillNotice')"
                       v-if="!parentItem.fillNotice"></i>
                    <i class="el-icon-document"
                       @click="handleAddMemo(parentIndex, undefined, 'fillNotice')"
                       style="color: #409EFF;"
                       v-else></i>
                  </template>
                </template>
              </div>
              <div class="item-isDefualt">
                <template>
                  <el-checkbox :disabled="parentItem.isDisabled"
                               v-model="parentItem.isDefualt"
                               :true-label="1"
                               :false-label="0"
                               @change="handleChangeDefault(parentIndex)"
                               ref="isDefualt"></el-checkbox>
                </template>
              </div>
              <div class="item-relate"
                   v-if="itemForm.questionType === 3">
                <el-checkbox :disabled="parentItem.isDisabled"
                             v-model="parentItem.relateType"
                             :true-label="2"
                             :false-label="0"></el-checkbox>
              </div>
              <div class="item-group">
                <el-checkbox v-model="parentItem.isGroup"
                             :true-label="1"
                             :false-label="0"
                             :disabled="parentItem.isDisabled || disableGroup"></el-checkbox>
                <template v-if='!parentItem.isDisabled && !disableGroup && parentItem.isGroup'>
                  <i class="el-icon-picture"
                     @click="handleUplodaGroup(parentIndex)"
                     v-if="!parentItem.groupImage"></i>
                  <img :src="parentItem.groupImage"
                       @click="handleUplodaGroup(parentIndex)"
                       v-else />
                </template>
              </div>
              <div class="item-recommend">
                <el-button type="text"
                           @click="handleRelateItem(parentItem)"
                           :disabled="!parentItem.optionNo || !!parentItem.optionItem.length">
                  关联推荐项目
                  <span v-if="parentItem.relateId">({{parentItem.relateId}})</span>
                </el-button>
              </div>
              <div class="item-action">
                <el-button-group>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-minus"
                             @click="handleDel(parentIndex)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-plus"
                             @click="handleAdd(parentIndex)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-top"
                             :disabled="!parentIndex"
                             @click="handleMove(parentIndex, 'up')"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-bottom"
                             :disabled="parentIndex == itemForm.optionItem.length - 1"
                             @click="handleMove(parentIndex, 'down')"></el-button>
                </el-button-group>
              </div>
            </div>
            <div class="item-children">
              <div class="item-children-list"
                   v-for="(childItem, childIndex) of parentItem.optionItem"
                   :key="childIndex">
                <div class="item-optionInfo">
                  <el-input placeholder="请输入文字..."
                            maxlength="100"
                            v-model="childItem.optionInfo"></el-input>
                </div>
                <div class="item-imgPath"
                     v-if="itemForm.questionType === 2 || itemForm.questionType === 3">
                  <template>
                    <i class="el-icon-picture"
                       @click="handleUplodaImg(parentIndex, childIndex)"
                       v-if="!childItem.imgPath"></i>
                    <img :src="childItem.imgPath"
                         @click="handleUplodaImg(parentIndex, childIndex)"
                         v-else />
                  </template>
                </div>
                <div class="item-memo"
                     v-if="itemForm.questionType === 2 || itemForm.questionType === 3">
                  <template>
                    <i class="el-icon-document"
                       @click="handleAddMemo(parentIndex, childIndex, 'memo')"
                       v-if="!childItem.memo"></i>
                    <i class="el-icon-document"
                       @click="handleAddMemo(parentIndex, childIndex, 'memo')"
                       style="color: #409EFF;"
                       v-else></i>
                  </template>
                </div>
                <div class="item-isFill"
                     v-if="itemForm.questionType !== 5">
                  <template>
                    <el-checkbox v-model="childItem.isFill"
                                 :true-label="1"
                                 :false-label="0"
                                 @change="handleChangeIsFull(parentIndex, childIndex)"></el-checkbox>
                    <el-checkbox v-model="childItem.isMust"
                                 :true-label="1"
                                 :false-label="0"
                                 v-if="childItem.isFill">是否必填</el-checkbox>
                    <template v-if="childItem.isFill">
                      <i class="el-icon-document"
                         @click="handleAddMemo(parentIndex, childIndex, 'fillNotice')"
                         v-if="!childItem.fillNotice"></i>
                      <i class="el-icon-document"
                         @click="handleAddMemo(parentIndex, childIndex, 'fillNotice')"
                         style="color: #409EFF;"
                         v-else></i>
                    </template>
                  </template>
                </div>
                <div class="item-isDefualt">
                  <template>
                    <el-checkbox v-model="childItem.isDefualt"
                                 :true-label="1"
                                 :false-label="0"
                                 @change="handleChangeDefault(parentIndex, childIndex)"></el-checkbox>
                  </template>
                </div>
                <div class="item-relate"
                     v-if="itemForm.questionType === 3">
                  <el-checkbox v-model="childItem.relateType"
                               :true-label="2"
                               :false-label="0"></el-checkbox>
                </div>
                <div class="item-group">
                  <el-checkbox :true-label="1"
                               :false-label="0"
                               v-model="childItem.isGroup"
                               :disabled="disableGroup"></el-checkbox>

                  <template v-if='!disableGroup && childItem.isGroup'>
                    <i class="el-icon-picture"
                       @click="handleUplodaGroup(parentIndex, childIndex)"
                       v-if="!childItem.groupImage"></i>
                    <img :src="childItem.groupImage"
                         @click="handleUplodaGroup(parentIndex, childIndex)"
                         v-else />
                  </template>
                </div>
                <div class="item-recommend">
                  <el-button type="text"
                             @click="handleRelateItem(childItem)"
                             :disabled="!childItem.optionNo">
                    关联推荐项目
                    <span v-if="childItem.relateId">({{childItem.relateId}})</span>
                  </el-button>
                </div>
                <div class="item-action">
                  <el-button-group>
                    <el-button type="warning"
                               circle
                               size="small"
                               icon="el-icon-minus"
                               @click="handleDel(parentIndex, childIndex)"></el-button>
                    <el-button type="warning"
                               circle
                               size="small"
                               icon="el-icon-plus"
                               @click="handleAdd(parentIndex, childIndex)"></el-button>
                    <el-button type="warning"
                               circle
                               size="small"
                               icon="el-icon-top"
                               :disabled="!childIndex"
                               @click="handleMove(parentIndex, 'up', childIndex)"></el-button>
                    <el-button type="warning"
                               circle
                               size="small"
                               icon="el-icon-bottom"
                               :disabled="childIndex == itemForm.optionItem[parentIndex].optionItem.length - 1"
                               @click="handleMove(parentIndex, 'down', childIndex)"></el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label>
          <el-button type="text"
                     @click="handleShowMome()">题目说明</el-button>
          <!-- <el-button type="text" @click="handleQsRelevance">问题关联设置</el-button> -->
          <el-button type="text"
                     @click="handleUplodaImgSubject">上传图片</el-button>
          <el-tooltip class="item"
                      effect="dark"
                      :content="qsTooltip"
                      placement="top-end">
            <el-button type="text"
                       @click="handleQsRelevance">问题关联设置</el-button>
          </el-tooltip>
          <el-button type="text"
                     @click="handleItemRelevance">选项关联设置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="modQuestion">完成编辑</el-button>
        </el-form-item>
      </el-form>
    </el-col>
    <el-dialog title="资源上传"
               :visible.sync="uploadFileDialog.visible"
               width="500px">
      <FileUpload :imgPath="uploadFileDialog"
                  @childSend="childSendfun" />
      <el-button v-if='uploadFileDialog.imgPath'
                 @click="clearImg"
                 type="primary">删除图片</el-button>
    </el-dialog>
    <el-dialog title="请填写说明"
               :visible.sync="momeDialog.visible"
               width="30%">
      <el-input type="textarea"
                :rows="3"
                maxlength="250"
                v-model="momeDialog.text"
                placeholder="请输入说明"></el-input>
      <div style="margin-top: 10px;">
        <el-checkbox v-if='momeDialog.type === "memo" && momeDialog.parentIndex === undefined'
                     v-model="momeDialog.isShowMemo"
                     :true-label="1"
                     :false-label="0">展示到页面</el-checkbox>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="handleSetMemoCancel">取 消</el-button>
        <el-button type="primary"
                   @click="handleSetMemo">确 认</el-button>
      </span>
    </el-dialog>
    <el-dialog title="题目关联"
               :visible.sync="qsRelevanceDialog"
               width="50%">
      <qsRelevance :qsRelevanceParam="qsRelevanceParam"
                   @childSendQs="childSendQsfun" />
    </el-dialog>
    <el-dialog title="选项关联"
               :visible.sync="itemRelevanceDialog"
               width="60%">
      <ItemRelevance :itemRelevanceParam="itemRelevanceParam"
                     @childSendItem="childSendItemfun" />
    </el-dialog>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'
import FileUpload from './../../components/FileUpload.vue'
import ChangeType from './ChangeType.vue'
import QsRelevance from './QsRelevance.vue'
import ItemRelevance from './ItemRelevance.vue'
import { qryTag } from './../../api/common'

@Component({
  name: 'Checkbox',
  components: {
    FileUpload,
    ChangeType,
    QsRelevance,
    ItemRelevance,
  },
})
class Checkbox extends Vue {
  // 设置刷新数据问题列表接口
  @Mutation('setQsListRefresh') setQsListRefresh

  // 父组件传进来的值
  @Prop() private childParams
  // 监控数据变化
  //immediate为true的时候，会刚开始时就执行一次，比如第一次渲染该页面
  //deep为true时，代表同时监听对象内部属性情况，内部变化，也会触发该函数
  @Watch('childParams', { immediate: true, deep: true })
  getChildParams(newVal) {
    // console.log("watch，checkbox组件", newVal);
    if (newVal) {
      this.qsRelevanceParam = this.itemRelevanceParam = this.propParams = newVal //传给子组件的参数
      this.itemForm.sortShow = newVal.sortShow
      if (newVal.options && newVal.options.length) {
        newVal.options.forEach((item) => {
          item.isEdit = false
        })
      }
      if (newVal.relateItem && newVal.relateItem.length) {
        const relateArray = []
        newVal.relateItem.forEach((item) => {
          let condition = ''
          if (item.optionInfo.includes(',') && item.isUnion) {
            condition = '中的全部选项时'
          } else if (item.optionInfo.includes(',') && !item.isUnion) {
            condition = '中的其中一个选项时'
          } else {
            condition = '时'
          }
          relateArray.push(
            `当题目${item.questionTitle}${
              item.isHide === 1 ? '（隐藏题）' : ''
            }，${item.isForward ? '选择' : '没有选择'}[${
              item.optionInfo
            }]${condition}`
          )
        })
        let unionType = ''
        if (relateArray.length > 1) {
          unionType = newVal.relateItem[0].unionType ? ' 且 ' : ' 或 '
        }
        this.qsTooltip = relateArray.join(unionType) + '此题才显示'
      } else {
        this.qsTooltip = ''
      }
      // 获取测试项目数量
      const questionId = this['$route'].query.id as string
      if (newVal.isEdit) this.getTextItemNum(newVal.subjectNo, questionId)

      // ----------------
      const thisitem = JSON.parse(JSON.stringify(this.item))
      if (!newVal.options || !newVal.options.length) {
        newVal.optionItem = [thisitem]
      } else {
        // 默认选择编辑的时候赋值。
        const isDefualtLen = this.childParams.options.filter((item) => {
          return item.isDefualt
        })
        if (isDefualtLen.length) this.isDefualt = '1'
        // 生成渲染列表的数据
        newVal.optionItem = []
        this.isDefualtArr = []
        newVal.options.forEach((item, index) => {
          if (item.isDefualt) this.isDefualtArr.push(index)
          if (!item.parentOptionNo) {
            item.optionItem = []
            newVal.optionItem.push(item)
          }
        })
        newVal.options.forEach((item) => {
          newVal.optionItem.forEach((childItem) => {
            if (item.parentOptionNo === childItem.optionNo) {
              childItem.optionItem.push(item)
            }
          })
        })
      }
      this.itemForm = newVal
      this.itemForm.isShowImg =
        newVal.isShowImg === undefined ? '0' : String(newVal.isShowImg)
      this.itemForm.isHide =
        newVal.isHide === undefined ? '0' : String(newVal.isHide)
      this.resetOptionItem()
    }
  }

  // 传给子组件的参数
  private propParams = {}

  // 上传文件弹窗
  private uploadFileDialog = {
    visible: false,
    imgPath: '',
    parentIndex: 0,
    childIndex: 0,
    isGroup: false,
  }
  // 说明弹窗
  private momeDialog = {
    type: '',
    visible: false,
    id: 0,
    text: '',
    parentIndex: undefined,
    childIndex: 0,
    isShowMemo: 0,
  }

  // 静态model
  private formModel = {}

  // 配置默认
  private isDefualt = ''
  private isDefualtArr = []

  private itemForm = {
    partNo: this.childParams.partNo,
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    questionType: this.childParams.questionType || 4,
    questionTitle: this.childParams.questionTitle,
    isMust: this.childParams.isMust || 0,
    isSimplifiedMode: this.childParams.isSimplifiedMode || 0,
    isSimplifiedMustFill: this.childParams.isSimplifiedMustFill || 0,
    isHot: this.childParams.isHot || 0,
    imgPath: this.childParams.imgPath,
    groupImage: this.childParams.groupImage,
    textSize: this.childParams.textSize || 3,
    uploadType: this.childParams.uploadType,
    sortShow: this.childParams.sortShow,
    memo: this.childParams.memo,
    fillMemo: this.childParams.fillMemo,
    isEdit: this.childParams.isEdit,
    // 问题选项集合
    options: this.childParams.options || [],
    optionItem: [],
    relateItem: [],
    isShowImg: '0',
    isHide: '0',
    isShowMemo: this.childParams.isShowMemo,
    labelCode: ''
  }

  // 单个问题项
  private item = {
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    optionInfo: '',
    imgPath: '',
    groupImage: '',
    memo: '',
    fillNotice: '',
    isFill: 0,
    isMust: 0,
    relateType: 0,
    isDefualt: 0,
    isGroup: 0,
    isDisabled: false,
    optionItem: [],
    relateItem: [],
    isShowImg: '0',
    isHide: '0',
    labelCode: '',
    isShowMemo: 0,
  }

  // 题目关联弹窗
  private qsRelevanceDialog = false
  private qsRelevanceParam
  private qsTooltip = ''

  // 选项关联
  private itemRelevanceDialog = false
  private itemRelevanceParam

  private disableGroup = false
  private loading = false

  // 取消必填
  handleIsSimplifiedMode(val: number): void {
    if (!val) this.itemForm.isSimplifiedMustFill = 0
  }

  // 关联测试项目
  handleRelateItem(item): void {
    const { optionNo } = item
    const subjectNo = this.itemForm.subjectNo
    const questionId = this['$route'].query.id
    // this["$router"].push({
    //   path: "testItem",
    //   query: {
    //     questionId,
    //     optionNo,
    //     subjectNo,
    //   },
    // });
    window.open(
      `testItem?questionId=${questionId}&optionNo=${optionNo}&subjectNo=${subjectNo}`
    )
  }

  // 显示说明添加
  handleShowMome(): void {
    const thisItem = JSON.parse(JSON.stringify(this.itemForm))
    this.momeDialog.text = thisItem.memo
    this.momeDialog.parentIndex = undefined
    this.momeDialog.childIndex = undefined
    this.momeDialog.visible = true
    this.momeDialog.type = 'memo'
    this.momeDialog.isShowMemo = thisItem.isShowMemo
  }

  // 编辑题目
  modQuestion(): void {
    // 上传前去除多余的参数，并添加sortShow
    delete this.itemForm.isEdit
    delete this.itemForm.options
    delete this.itemForm.relateItem
    if (this.itemForm.optionItem.length) {
      this.itemForm.optionItem.forEach((item, index) => {
        item.sortShow = index
        if (item.optionItem && item.optionItem.length) {
          item.optionItem.forEach((childItem, childrenIndex) => {
            childItem.sortShow = String(index + 1) + childrenIndex
          })
        }
      })
    }
    if (!this.itemForm.questionTitle) {
      this['$message'].error('问题标题不能为空')
    } else {
      let flag = 0
      this.itemForm.optionItem.filter((item) => {
        if (!item.optionInfo) flag++
        if (item.optionItem && item.optionItem.length) {
          item.optionItem.forEach((cItem) => {
            if (!cItem.optionInfo) flag++
          })
        }
      })
      if (flag) {
        this['$message'].error('选项标题不能为空')
      } else {
        ajaxNew('/business/api.v1.question/qsubject/mod', this.itemForm, 'post')
          .then((res) => {
            if (res.resultCode === '0') {
              this.childParams.isEdit = false
              this.setQsListRefresh(true)
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            this['$message'].error(e)
          })
      }
    }
  }

  // 子选项的相关操作
  handleDel(parentIndex: number, childrenIndex?: number): void {
    if (childrenIndex === undefined) {
      if (this.itemForm.optionItem.length === 1) {
        this['$message'].error('已经是最后一条了,不能在删除.')
      } else {
        this['$confirm'](`您确认删除？`)
          .then(() => {
            this.delOption(this.itemForm.optionItem[parentIndex])
            this.itemForm.optionItem.splice(parentIndex, 1)
            this.resetOptionItem()
          })
          .catch(() => {
            // this["$message"].error(e);
          })
      }
    } else {
      this['$confirm'](`您确认删除？`)
        .then(() => {
          this.delOption(
            this.itemForm.optionItem[parentIndex].optionItem[childrenIndex]
          )
          this.itemForm.optionItem[parentIndex].optionItem.splice(
            childrenIndex,
            1
          )
          this.resetOptionItem()
        })
        .catch(() => {
          // this["$message"].error(e);
        })
    }
  }
  handleAdd(parentIndex: number, childIndex?: number): void {
    const item = JSON.parse(JSON.stringify(this.item))
    // 插入在当前点击按钮的下一个
    if (childIndex === undefined) {
      item.optionInfo = `选项名称${this.itemForm.optionItem.length}`
      this.itemForm.optionItem.splice(parentIndex + 1, 0, item)
    } else {
      if (!this.itemForm.optionItem[parentIndex].relateId) {
        item.optionInfo = `子选项-${this.itemForm.optionItem[parentIndex].optionItem.length}`
        this.itemForm.optionItem[parentIndex].optionItem.splice(
          childIndex + 1,
          0,
          item
        )
      } else {
        this['$message'].error('选项已关联推荐项目，请删除后再添加子选项')
      }
    }
    this.resetOptionItem()
  }
  handleMove(parentIndx: number, arrow: string, childIndex?: number): void {
    if (childIndex === undefined) {
      if (arrow === 'up') {
        this.itemForm.optionItem[parentIndx] = this.itemForm.optionItem.splice(
          parentIndx - 1,
          1,
          this.itemForm.optionItem[parentIndx]
        )[0]
      } else if (arrow === 'down') {
        this.itemForm.optionItem[parentIndx] = this.itemForm.optionItem.splice(
          parentIndx + 1,
          1,
          this.itemForm.optionItem[parentIndx]
        )[0]
      }
    } else {
      if (arrow === 'up') {
        this.itemForm.optionItem[parentIndx].optionItem[childIndex] =
          this.itemForm.optionItem[parentIndx].optionItem.splice(
            childIndex - 1,
            1,
            this.itemForm.optionItem[parentIndx].optionItem[childIndex]
          )[0]
      } else if (arrow === 'down') {
        this.itemForm.optionItem[parentIndx].optionItem[childIndex] =
          this.itemForm.optionItem[parentIndx].optionItem.splice(
            childIndex + 1,
            1,
            this.itemForm.optionItem[parentIndx].optionItem[childIndex]
          )[0]
      }
    }
    this.resetOptionItem()
  }

  // 删除选项
  delOption(option): void {
    if (option.subjectNo && option.optionNo) {
      this.loading = true
      ajaxNew(
        '/business/api.v1.question/qoption/del',
        {
          partNo: this.itemForm.partNo,
          subjectNo: option.subjectNo,
          optionNo: option.optionNo,
        },
        'post'
      )
        .then((res) => {
          if (res.resultCode === '0') {
            // this.childParams.isEdit = false;
            this.resetOptionItem()
            this.loading = false
          } else {
            this['$message'].error(res.resultMsg)
            this.loading = false
          }
        })
        .catch((e) => {
          this['$message'].error(e)
          this.loading = false
        })
    }
  }

  // 获取测试项目数量
  getTextItemNum(subjectNo, questionId): void {
    ajaxNew(
      '/business/api.v1.question/qitem/qrySum',
      {
        subjectNo,
        questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const relList = res.data.items
          const items = this.itemForm.optionItem
          relList.forEach((rel) => {
            items.forEach((qs) => {
              if (qs.optionNo === rel.optionNo) {
                qs = Object.assign(qs, rel)
              }
              if (qs.optionItem && qs.optionItem.length) {
                qs.optionItem.forEach((qs2) => {
                  if (qs2.optionNo === rel.optionNo) {
                    qs2 = Object.assign(qs2, rel)
                    // qs.childrenHasItem = true
                  }
                })
              }
            })
          })
          this.resetOptionItem()
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 题目上传图片
  handleUplodaImgSubject() {
    this.uploadFileDialog.isGroup = false
    this.uploadFileDialog.visible = true
    this.uploadFileDialog.parentIndex = undefined
    this.uploadFileDialog.childIndex = undefined
    this.uploadFileDialog.imgPath = this.itemForm.imgPath
  }

  // 上传图片
  handleUplodaImg(parentIndex: number, childIndex?: number): void {
    this.uploadFileDialog.isGroup = false
    this.uploadFileDialog.visible = true
    this.uploadFileDialog.parentIndex = parentIndex
    if (childIndex === undefined) {
      this.uploadFileDialog.imgPath =
        this.itemForm.optionItem[parentIndex].imgPath
      this.uploadFileDialog.childIndex = undefined
    } else {
      this.uploadFileDialog.imgPath =
        this.itemForm.optionItem[parentIndex].optionItem[childIndex].imgPath
      this.uploadFileDialog.childIndex = childIndex
    }
    console.log(this.uploadFileDialog)
  }

  // 上传关联分组的图片
  handleUplodaGroup(parentIndex: number, childIndex?: number): void {
    this.uploadFileDialog.isGroup = true
    this.uploadFileDialog.visible = true
    this.uploadFileDialog.parentIndex = parentIndex
    if (childIndex === undefined) {
      this.uploadFileDialog.imgPath =
        this.itemForm.optionItem[parentIndex].groupImage
      this.uploadFileDialog.childIndex = undefined
    } else {
      this.uploadFileDialog.imgPath =
        this.itemForm.optionItem[parentIndex].optionItem[childIndex].groupImage
      this.uploadFileDialog.childIndex = childIndex
    }
    // this.uploadFileDialog.groupImage =
    //   this.itemForm.optionItem[parentIndex].groupImage
  }

  // 接收子组件的返回值
  childSendfun(res: string) {
    if (!this.uploadFileDialog.isGroup) {
      if (
        this.uploadFileDialog.childIndex === undefined &&
        this.uploadFileDialog.parentIndex === undefined
      ) {
        // 题目上的图片
        this.itemForm.imgPath = res
      } else if (this.uploadFileDialog.childIndex === undefined) {
        // 父选项上的图片
        this.itemForm.optionItem[this.uploadFileDialog.parentIndex].imgPath =
          res
      } else {
        // 子选项上的图片
        this.itemForm.optionItem[this.uploadFileDialog.parentIndex].optionItem[
          this.uploadFileDialog.childIndex
        ].imgPath = res
      }
    } else {
      if (this.uploadFileDialog.childIndex === undefined) {
        this.itemForm.optionItem[this.uploadFileDialog.parentIndex].groupImage =
          res
      } else {
        this.itemForm.optionItem[this.uploadFileDialog.parentIndex].optionItem[
          this.uploadFileDialog.childIndex
        ].groupImage = res
      }
    }
  }
  // 接收子组件的返回值
  childSendQsfun(res): void {
    if (res) {
      this.qsRelevanceDialog = false
    }
  }
  //接收子组件的返回值
  childSendItemfun(res): void {
    if (res) {
      this.itemRelevanceDialog = false
    }
  }

  // 编辑说明
  handleAddMemo(parentIndex: number, childIndex?: number, type?): void {
    this.momeDialog.visible = true
    this.momeDialog.type = type
    this.momeDialog.parentIndex = parentIndex
    if (childIndex === undefined) {
      this.momeDialog.text = this.itemForm.optionItem[parentIndex][type]
      this.momeDialog.childIndex = undefined
    } else {
      this.momeDialog.text =
        this.itemForm.optionItem[parentIndex].optionItem[childIndex][type]
      this.momeDialog.childIndex = childIndex
    }
  }

  // 设置说明
  handleSetMemo(): void {
    if (
      this.momeDialog.parentIndex === undefined &&
      this.momeDialog.childIndex === undefined
    ) {
      // 题目说明
      console.log(this.itemForm, this.momeDialog)
      this.itemForm[this.momeDialog.type] = this.momeDialog.text
      this.itemForm.isShowMemo = this.momeDialog.isShowMemo
    } else if (
      this.momeDialog.childIndex === undefined &&
      this.momeDialog.parentIndex !== undefined
    ) {
      // 父级说明
      this.itemForm.optionItem[this.momeDialog.parentIndex][
        this.momeDialog.type
      ] = this.momeDialog.text
    } else {
      // 子集说明
      this.itemForm.optionItem[this.momeDialog.parentIndex].optionItem[
        this.momeDialog.childIndex
      ][this.momeDialog.type] = this.momeDialog.text
    }
    this.momeDialog.visible = false
  }

  // 取消说明弹窗
  handleSetMemoCancel(): void {
    this.momeDialog.text = ''
    this.momeDialog.visible = false
  }

  // 切换默认选项(默认选中唯一)
  handleChangeDefault(parentIndex: number, childIndex?: number): void {
    if (this.itemForm.questionType === 2 || this.itemForm.questionType === 4) {
      if (childIndex === undefined) {
        if (this.itemForm.optionItem[parentIndex].isDefualt) {
          // this.itemForm.optionItem.forEach(itemCurr => {
          //   itemCurr.isDefualt = 0;
          // });
          // 子母子允许选中一个
          this.itemForm.optionItem.forEach((p) => {
            p.isDefualt = 0
            if (p.optionItem && p.optionItem.length) {
              p.optionItem.forEach((c) => {
                c.isDefualt = 0
              })
            }
          })
          this.itemForm.optionItem[parentIndex].isDefualt = 1
          this.isDefualt = '1'
        } else {
          this.isDefualt = ''
        }
      } else {
        if (
          this.itemForm.optionItem[parentIndex].optionItem[childIndex].isDefualt
        ) {
          // 子母子允许选中一个
          this.itemForm.optionItem.forEach((p) => {
            p.isDefualt = 0
            if (p.optionItem && p.optionItem.length) {
              p.optionItem.forEach((c) => {
                c.isDefualt = 0
              })
            }
          })
          // this.itemForm.optionItem[parentIndex].optionItem.forEach(itemCurr => {
          //   itemCurr.isDefualt = 0;
          // });
          this.itemForm.optionItem[parentIndex].optionItem[
            childIndex
          ].isDefualt = 1
          this.isDefualt = '1'
        } else {
          this.isDefualt = ''
        }
      }
    } else {
      this.isDefualtArr = []
      this.itemForm.optionItem.forEach((item, index) => {
        if (item.isDefualt) this.isDefualtArr.push(index)
      })
    }
  }

  // 插入子选项
  handleInserChildItem(parentIndex: number): void {
    if (!this.itemForm.optionItem[parentIndex].relateId) {
      const item = JSON.parse(JSON.stringify(this.item))
      item.optionInfo = `子选项-${this.itemForm.optionItem[parentIndex].optionItem.length}`
      this.itemForm.optionItem[parentIndex].optionItem.push(item)
      this.resetOptionItem()
    } else {
      this['$message'].error('选项已关联推荐项目，请删除后再添加子选项')
    }
  }

  // 切换允许填空
  handleChangeIsFull(parentIndex: number, childIndex?: number): void {
    if (childIndex === undefined) {
      if (!this.itemForm.optionItem[parentIndex].isFill)
        this.itemForm.optionItem[parentIndex].isMust = 0
    } else {
      if (!this.itemForm.optionItem[parentIndex].optionItem[childIndex].isFill)
        this.itemForm.optionItem[parentIndex].optionItem[childIndex].isMust = 0
    }
    this.resetOptionItem()
  }

  // 点击问题关联设置
  handleQsRelevance(): void {
    this.qsRelevanceDialog = true
  }

  // 点击选项关联设置
  handleItemRelevance(): void {
    this.itemRelevanceDialog = true
  }

  // 动态控制父级按钮状态
  setGroupStatus(): void {
    this.itemForm.optionItem.forEach((pItem) => {
      if (pItem.optionItem && pItem.optionItem.length) {
        pItem.isFill = 0
        pItem.isMust = 0
        pItem.isDefualt = 0
        pItem.isGroup = 0
        pItem.isDisabled = true
      } else {
        pItem.isDisabled = false
      }
    })
  }

  // 强制刷新数据
  resetOptionItem(): void {
    this.setGroupStatus()
    this.itemForm = JSON.parse(JSON.stringify(this.itemForm))
  }

  // 删除图片
  clearImg() {
    console.log(this.uploadFileDialog)
    debugger
    const key = this.uploadFileDialog.isGroup ? 'groupImage' : 'imgPath'
    if (
      this.uploadFileDialog.parentIndex === undefined &&
      this.uploadFileDialog.childIndex === undefined
    ) {
      this.itemForm.imgPath = ''
      this.uploadFileDialog.imgPath = ''
    } else if (
      this.uploadFileDialog.childIndex === undefined &&
      this.uploadFileDialog.parentIndex !== undefined
    ) {
      this.itemForm.optionItem[this.uploadFileDialog.parentIndex][key] = ''
      this.uploadFileDialog.imgPath = ''
    } else {
      this.itemForm.optionItem[this.uploadFileDialog.parentIndex].optionItem[
        this.uploadFileDialog.childIndex
      ][key] = ''
      this.uploadFileDialog.imgPath = ''
    }
  }

  private labels = [] as any
  mounted(): void {
    // const thisitem = JSON.parse(JSON.stringify(this.item));
    // if (!this.itemForm.options || !this.itemForm.options.length) {
    //   this.itemForm.optionItem = [thisitem];
    // } else {
    //   // 默认选择编辑的时候赋值。
    //   const isDefualtLen = this.childParams.options.filter((item) => {
    //     return item.isDefualt;
    //   });
    //   if (isDefualtLen.length) this.isDefualt = "1";
    //   // 生成渲染列表的数据
    //   this.itemForm.options.forEach((item, index) => {
    //     if (item.isDefualt) this.isDefualtArr.push(index);
    //     if (!item.parentOptionNo) {
    //       item.optionItem = [];
    //       this.itemForm.optionItem.push(item);
    //     }
    //   });
    //   this.itemForm.options.forEach((item) => {
    //     this.itemForm.optionItem.forEach((childItem) => {
    //       if (item.parentOptionNo === childItem.optionNo) {
    //         childItem.optionItem.push(item);
    //       }
    //     });
    //   });
    // }
    // this.resetOptionItem();

    // 首页和最后章节禁用点分组功能
    if (this['$route'].name === 'Step2' || this['$route'].name === 'Step4') {
      this.disableGroup = true
    }
    qryTag({}).then((res) => {
      this.labels = res.filter((v: any) => v.labelCode !== 'category')
    })
  }
}
export default Checkbox
</script>

<style>
.el-table .cell {
  display: flex;
  align-items: center;
}
</style>