<template>
  <el-row>
    <el-col :span="24">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item :label="itemForm.sortShow + '.'">
          <span v-if="itemForm.isMust"
                class="must">*</span>
          {{ itemForm.questionTitle || '标题' }}
          <img style="width: 20px; height: 20px; margin: 0 3px; cursor: pointer;"
               v-if="itemForm.imgPath"
               :src="itemForm.imgPath"
               @click='uploadFileDialog = true' />
          <span style="color: #ccc;">[图片上传]</span>
        </el-form-item>
        <el-form-item v-if="itemForm.memo">{{ itemForm.memo }}</el-form-item>
        <el-form-item v-if="qsTooltip">{{ qsTooltip }}</el-form-item>
        <el-form-item>
          <el-input disabled
                    type="textarea"
                    :rows="10 / itemForm.textSize"
                    :placeholder="itemForm.fillMemo"></el-input>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="24"
            v-if="childParams.isEdit"
            class="question_eidt">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item label="问题标题">
          <el-input maxlength="300"
                    v-model="itemForm.questionTitle"
                    placeholder="请输入问题标题..."
                    style="width: 300px; margin-right: 15px;"></el-input>
          <el-checkbox v-model="itemForm.isMust"
                       :true-label="1"
                       :false-label="0">PC必填</el-checkbox>
          <el-checkbox v-model="itemForm.isSimplifiedMode"
                       :true-label="1"
                       :false-label="0"
                       @change="handleIsSimplifiedMode">移动端展示</el-checkbox>
          <el-checkbox v-model="itemForm.isSimplifiedMustFill"
                       :true-label="1"
                       :false-label="0"
                       v-if="itemForm.isSimplifiedMode">移动端必填</el-checkbox>
          <el-button type="text"
                     style="float: right;"
                     @click="handleRelateItem">
            关联推荐项目
            <span v-if="itemForm.relateId">({{itemForm.relateId}})</span>
          </el-button>
        </el-form-item>
        <el-form-item label="编辑框大小">
          <el-radio-group v-model="itemForm.textSize">
            <el-radio :label="1">大</el-radio>
            <el-radio :label="2">中</el-radio>
            <el-radio :label="3">小</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="图片上传数">
          <el-select v-model='uploadNum'
                     @change="handleChangeNum">
            <el-option v-for="i of 5"
                       :label="i"
                       :key="i"
                       :value="i">{{i}}</el-option>
          </el-select>
          <el-button @click="handleShowImgMemo"
                     type="text"
                     style="margin-left: 20px;">上传图片提示文案</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button type="text"
                     @click="handleShowMome('memo')">题目说明</el-button>
          <el-button type="text"
                     @click="uploadFileDialog = true">上传图片</el-button>
          <el-button type="text"
                     @click="handleShowMome('fillMemo')">编辑说明</el-button>
        </el-form-item>
        <el-form-item label>
          <el-tooltip class="item"
                      effect="dark"
                      :content="qsTooltip"
                      placement="top-end">
            <el-button type="text"
                       @click="handleQsRelevance">问题关联设置</el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="modQuestion">完成编辑</el-button>
        </el-form-item>
      </el-form>
    </el-col>
    <el-dialog :title="'请填写' + momeDialog.title"
               :visible.sync="momeDialog.visible"
               width="30%">
      <el-input maxlength="250"
                type="textarea"
                :rows="3"
                v-model="momeDialog.value"
                :placeholder="'请输入'+ momeDialog.title"></el-input>
      <div style="margin-top: 10px;"
           v-if='momeDialog.type ==="memo"'>
        <el-checkbox v-model="momeDialog.isShowMemo"
                     :true-label="1"
                     :false-label="0">展示到页面</el-checkbox>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="momeDialog.visible = false">取 消</el-button>
        <el-button type="primary"
                   @click="handleModal">确 认</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源上传"
               :visible.sync="uploadFileDialog"
               width="500px">
      <FileUpload :imgPath="itemForm"
                  @childSend="childSendfun" />
      <el-button @click="clearImg"
                 type="primary">删除图片</el-button>
    </el-dialog>
    <el-dialog title="题目关联"
               :visible.sync="qsRelevanceDialog"
               width="50%">
      <qsRelevance :qsRelevanceParam="qsRelevanceParam"
                   @childSendQs="childSendQsfun" />
    </el-dialog>
    <el-dialog title="编辑图片上传提示文案"
               :visible.sync="uploadDialog"
               width="50%">
      <div class="uploadTips"
           v-for='(item, index) of itemForm.optionItem'
           :key='index'>
        <span class="label">图片{{index + 1}}</span>
        <div class="tips">
          <el-input placeholder="最多输入8个字，默认为上传图片"
                    v-model="item.optionInfo"
                    maxlength="8" />
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary"
                   @click="uploadDialog = false">保存</el-button>
        <el-button @click="uploadDialog = false">取消</el-button>
      </div>
    </el-dialog>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'
import FileUpload from './../../components/FileUpload.vue'
import ChangeType from './ChangeType.vue'
import QsRelevance from './QsRelevance.vue'

@Component({
  name: 'Input',
  components: {
    FileUpload,
    ChangeType,
    QsRelevance,
  },
})
class Input extends Vue {
  @Mutation('setQsListRefresh') setQsListRefresh
  // 父组件传进来的值
  @Prop() private childParams

  // 监控数据变化
  //immediate为true的时候，会刚开始时就执行一次，比如第一次渲染该页面
  //deep为true时，代表同时监听对象内部属性情况，内部变化，也会触发该函数
  @Watch('childParams', { immediate: true, deep: true })
  getChildParams(newVal) {
    if (newVal) {
      this.itemForm.sortShow = newVal.sortShow
      this.qsRelevanceParam = newVal
      // 关联关系
      if (newVal.relateItem && newVal.relateItem.length) {
        const relateArray = []
        newVal.relateItem.forEach((item) => {
          // 当题目1. 题目1选择[选项1]时，此题才显示
          // 当题目1. 题目1选择[选项1]时，当题目2.题目2选择[选项4]时，此题才显示
          // 当题目1. 题目1选择[选项1]时，当题目2.题目2选择[选项4]、[选项5]中的全部选项时，此题才显示
          // 当题目1. 题目1选择[选项1]时，当题目2.题目2选择[选项4]、[选项5]中的其中一个选项时，此题才显示
          let condition = ''
          if (item.optionInfo.includes(',') && item.isUnion) {
            condition = '中的全部选项时'
          } else if (item.optionInfo.includes(',') && !item.isUnion) {
            condition = '中的其中一个选项时'
          } else {
            condition = '时'
          }
          relateArray.push(
            `当题目${item.questionTitle}，${
              item.isForward ? '选择' : '没有选择'
            }[${item.optionInfo}]${condition}`
          )
        })
        let unionType = ''
        if (relateArray.length > 1) {
          unionType = newVal.relateItem[0].unionType ? ' 且 ' : ' 或 '
        }
        this.qsTooltip = relateArray.join(unionType) + '此题才显示'
      } else {
        this.qsTooltip = ''
      }
      // 获取测试项目数量
      const questionId = this['$route'].query.id as string
      if (newVal.isEdit) this.getTextItemNum(newVal.subjectNo, questionId)
      if (this.itemForm.optionItem.length) {
        this.uploadNum = this.itemForm.optionItem.length
      } else {
        // 创建1条数据到选项集合
        const item = JSON.parse(JSON.stringify(this.item))
        this.itemForm.optionItem.push(item)
      }
    }
  }

  private formModel = {}

  // 传给子组件的参数
  private propParams = {
    partNo: this.childParams.partNo,
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    questionType: this.childParams.questionType,
    questionTitle: this.childParams.questionTitle || '问题标题',
    isMust: this.childParams.isMust,
    isSimplifiedMode: this.childParams.isSimplifiedMode,
    isSimplifiedMustFill: this.childParams.isSimplifiedMustFill,
    isHot: this.childParams.isHot,
    imgPath: this.childParams.imgPath,
    textSize: this.childParams.textSize,
    uploadType: this.childParams.uploadType,
    sortShow: this.childParams.sortShow,
    memo: this.childParams.memo,
    fillMemo: this.childParams.fillMemo,
    optionItem: this.childParams.options,
    relateType: this.childParams.relateType,
  }

  // 上传文件弹窗
  private uploadFileDialog = false

  // 编辑参数
  private itemForm = {
    partNo: this.childParams.partNo,
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    questionType: this.childParams.questionType,
    questionTitle: this.childParams.questionTitle || '问题标题',
    isMust: this.childParams.isMust || 0,
    isSimplifiedMode: this.childParams.isSimplifiedMode || 0,
    isSimplifiedMustFill: this.childParams.isSimplifiedMustFill || 0,
    isHot: this.childParams.isHot || 0,
    imgPath: this.childParams.imgPath,
    textSize: this.childParams.textSize || 3,
    uploadType: this.childParams.uploadType,
    sortShow: this.childParams.sortShow,
    memo: this.childParams.memo,
    fillMemo: this.childParams.fillMemo,
    optionItem: this.childParams.options || [],
    isEdit: this.childParams.isEdit,
    relateId: 0,
    isShowMemo: this.childParams.isShowMemo,
  }

  // 说明弹窗
  private momeDialog = {
    type: '',
    title: '',
    value: '',
    visible: false,
    isShowMemo: 0,
  }

  // 题目关联弹窗
  private qsRelevanceDialog = false
  private qsRelevanceParam = {}
  private qsTooltip = ''

  // 上传图片数
  private uploadNum = 1
  private uploadDialog = false
  private item = {
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    optionInfo: '',
    imgPath: '',
    groupImage: '',
    memo: '',
    fillNotice: '',
    isFill: 0,
    isMust: 0,
    relateType: 0,
    isDefualt: 0,
    isGroup: 0,
    isDisabled: false,
    optionItem: [],
    relateItem: [],
    isShowImg: '0',
    isHide: '0',
    isShowMemo: 0,
  }

   // 取消必填
   handleIsSimplifiedMode(val: number): void {
    if (!val) this.itemForm.isSimplifiedMustFill = 0
  }

  // 关联测试项目
  handleRelateItem(): void {
    const subjectNo = this.itemForm.subjectNo
    const questionId = this['$route'].query.id
    // this["$router"].push({
    //   path: "testItem",
    //   query: {
    //     questionId,
    //     subjectNo,
    //   },
    // });
    window.open(`testItem?questionId=${questionId}&subjectNo=${subjectNo}`)
  }

  // 接收子组件的返回值
  childSendfun(res: string) {
    if (res) this.itemForm.imgPath = res
  }

  childSendQsfun(res): void {
    if (res) {
      this.qsRelevanceDialog = false
    }
  }

  // 获取测试项目数量
  getTextItemNum(subjectNo, questionId): void {
    ajaxNew(
      '/business/api.v1.question/qitem/qrySum',
      {
        subjectNo,
        questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const relList = res.data.items
          this.itemForm.relateId = relList.length ? relList[0].relateId : 0
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 显示说明添加
  handleShowMome(type: string): void {
    const thisItem = JSON.parse(JSON.stringify(this.itemForm))
    if (type === 'memo') {
      this.momeDialog.title = '题目说明'
      this.momeDialog.value = thisItem.memo
      this.momeDialog.isShowMemo = thisItem.isShowMemo
    } else {
      this.momeDialog.title = '编辑说明'
      this.momeDialog.value = thisItem.fillMemo
    }
    this.momeDialog.type = type
    this.momeDialog.visible = true
  }

  // 编辑题目
  modQuestion(): void {
    if (!this.itemForm.questionTitle) {
      this['$message'].error('问题标题不能为空')
    } else {
      ajaxNew('/business/api.v1.question/qsubject/mod', this.itemForm, 'post')
        .then((res) => {
          if (res.resultCode === '0') {
            this.childParams.isEdit = false
            this.setQsListRefresh(true)
          } else {
            this['$message'].error(res.resultMsg)
          }
        })
        .catch((e) => {
          this['$message'].error(e)
        })
    }
  }

  // 确认说明按点击
  handleModal() {
    this.momeDialog.visible = false
    if (this.momeDialog.type === 'memo') {
      this.itemForm.memo = this.momeDialog.value
      this.itemForm.isShowMemo = this.momeDialog.isShowMemo
    } else {
      this.itemForm.fillMemo = this.momeDialog.value
    }
  }

  // 点击问题关联
  handleQsRelevance() {
    this.qsRelevanceDialog = true
  }

  // 删除图片
  clearImg() {
    this.itemForm.imgPath = ''
  }

  // 提示弹窗
  handleShowImgMemo() {
    this.uploadDialog = true
  }

  // 实时更新选项数量
  handleChangeNum(val) {
    this.uploadNum = val
    if (this.itemForm.optionItem.length > val) {
      this.itemForm.optionItem = this.itemForm.optionItem.splice(0, val)
    } else if (this.itemForm.optionItem.length < val) {
      let tempArr = []
      for (let i = 0; i < val - this.itemForm.optionItem.length; i++) {
        tempArr.push(JSON.parse(JSON.stringify(this.item)))
      }
      this.itemForm.optionItem = [...this.itemForm.optionItem, ...tempArr]
    }
  }

  mounted(): void {}
}
export default Input
</script>

<style lang="less" scoped>
.must {
  color: #f00;
}
.uploadTips {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .label {
    display: flex;
    width: 60px;
  }
  .tips {
    display: flex;
    width: 100%;
  }
}
</style>