<template>
  <el-form class="='relevance'"
           v-loading='loading'
           ref="form"
           :model="formModel"
           label-width="100px">
    <el-form-item label="当前题目"
                  v-if="!qsRelevanceParam.source">
      {{ qsRelevanceParam.sortShow }}.
      {{ qsRelevanceParam.questionTitle }}
    </el-form-item>
    <el-form-item label="规则名称"
                  v-if="qsRelevanceParam.source === 'group' || qsRelevanceParam.source === 'groupByHome'">
      <el-input placeholder="请输入规则名称"
                maxlength="20"
                v-model="setRelevanceGroupParams.groupName"></el-input>
    </el-form-item>
    <template v-for="(list, listIndex) of listArray">
      <el-form-item :label="'关联题目' + (listIndex + 1)"
                    :key="listIndex">
        <el-select v-model="list.subjectNo"
                   @change="
            (val) => {
              handleChange(val, listIndex);
            }
          ">
          <el-option v-for="(item, index) of qsList"
                     :key="index"
                     :value="item.subjectNo"
                     :label="`${item.sortShow }.${item.questionTitle}[${item.questionTypeName }]${item.isHide === 1 ? '（隐藏题）' : '' }`"></el-option>
        </el-select>
        <el-button style="margin-left: 10px;"
                   v-if="!listIndex"
                   @click="handleAdd">+更多</el-button>
        <el-button style="margin-left: 10px;"
                   v-else
                   @click="handleDel(listIndex)">-取消</el-button>
        <el-row>
          <!-- 单选题 -->
          <el-col :span="24"
                  v-if="
              (list.questionType === 2 || list.questionType === 4) &&
              list.options.length
            ">
            <div>当“{{ list.questionTitle }}”选择下面的选项：</div>
            <el-checkbox :indeterminate="list.isIndeterminate"
                         v-model="list.checkAll"
                         @change="
                (val) => {
                  handleCheckAllChange(val, listIndex);
                }
              ">全选</el-checkbox>
            <div style="margin: 15px 0"></div>
            <el-checkbox-group v-model="list.optionNos"
                               @change="
                (val) => {
                  handleCheckedOptionChange(val, listIndex);
                }
              "
                               class="relevance_checkbox">
              <el-checkbox v-for="(option, optionIndex) in list.options"
                           :label="option.optionNo"
                           :key="optionIndex">{{ option.optionInfo }}</el-checkbox>
            </el-checkbox-group>
            <div>中的任意一个时才出现</div>
          </el-col>
          <!-- 多选题 -->
          <el-col :span="24"
                  v-if="
              (list.questionType === 3 || list.questionType === 5) &&
              list.options.length
            ">
            <div>
              当“{{ list.questionTitle }}”
              <el-select v-model="list.isForward"
                         v-if='source !== "group" && source !== "groupByHome"'>
                <el-option value="1"
                           label="选择">选择</el-option>
                <el-option value="0"
                           label="没有选择">没有选择</el-option>
              </el-select>选择下面的选项：
            </div>
            <el-checkbox :indeterminate="list.isIndeterminate"
                         v-model="list.checkAll"
                         @change="
                (val) => {
                  handleCheckAllChange(val, listIndex);
                }
              ">全选</el-checkbox>
            <div style="margin: 15px 0"></div>
            <el-checkbox-group v-model="list.optionNos"
                               @change="
                (val) => {
                  handleCheckedOptionChange(val, listIndex);
                }
              "
                               class="relevance_checkbox">
              <el-checkbox v-for="(option, optionIndex) in list.options"
                           :label="option.optionNo"
                           :key="optionIndex">{{ option.optionInfo }}</el-checkbox>
            </el-checkbox-group>
            <div>
              <el-select v-model="list.isUnion">
                <el-option value="0"
                           label="其中一个">其中一个</el-option>
                <el-option value="1"
                           label="全部选项">全部选项</el-option>
              </el-select>时才出现
            </div>
          </el-col>
        </el-row>
      </el-form-item>
    </template>
    <el-form-item label="关联多题时"
                  v-if="listArray.length > 1">
      <div>多题之间</div>
      <el-radio-group v-model="setRelevanceParams.unionType">
        <el-radio :label="1">为“且”的关系( ? )</el-radio>
        <el-radio :label="0">为“或”的关系( ? )</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="qsList.length > 0">
      <el-button type="text"
                 @click="delRelevance"
                 :disabled='!setRelevanceParams.groupNo'>删除所有关联</el-button>
      <el-button type="primary"
                 @click="setRelevance">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator'
import { ajaxNew } from './../../api/index'
import { Mutation } from 'vuex-class'
import { qryByPositionAction } from './../../api/common'

@Component({
  name: 'QsRelevance',
})
class QsRelevance extends Vue {
  @Prop() private qsRelevanceParam
  @Mutation('setQsListRefresh') setQsListRefresh
  @Emit('childSendQs') emitParentFun(data) {}
  @Watch('qsRelevanceParam', { immediate: true, deep: true })
  getqsRelevanceParam(newVal) {
    if (newVal) {
      this.loading = true
      newVal = JSON.parse(JSON.stringify(newVal))
      this.qsList = []
      this.setRelevanceParams.questionId = this.getRelevanceParams.questionId =
        this['$route'].query.id as string
      this.getRelevanceParams.partNo = this.setRelevanceParams.partNo =
        newVal.partNo
      this.getRelevanceParams.subjectNo = this.setRelevanceParams.subjectNo =
        newVal.subjectNo
      this.source = newVal.source
      if (this.source === 'option') {
        // 选项关联
        this.getRelevanceParams.optionNo = newVal.optionNo
        this.getQsRelevanceList()
      } else if (this.source === 'card') {
        // 9宫格问题关联
        this.getRelevanceParams.optionNo = newVal.optionNo
        this.qryByQuestion(this['$route'].query.id as string)
      } else if (this.source === 'cardRelevance') {
        this.getRelevanceParams.optionNo = newVal.optionNo
        // 根据id查询首页题目
        this.qryByPosition()
        // 12宫格问卷
        setTimeout(() => {
          this.getQsRelevanceList()
        }, 1000)
      } else if (this.source === 'group') {
        this.groupRelevance = newVal.groupRelevance
        // 12宫格规则关联
        this.qryByPosition()
        // 12宫格问卷
        setTimeout(() => {
          this.getQsRelevanceList()
        }, 1000)
      } else if (this.source === 'groupByHome') {
        this.groupRelevance = newVal.groupRelevance
        this.getQsRelevanceList()
      } else {
        // 正常问题关联
        this.getQsRelevanceList()
      }
    }
  }

  private itemForm = this.qsRelevanceParam
  private formModel = {}
  // 可关联题目列表
  private qsList = []
  // 可关联题目总数
  private qsTotal = 5
  // 创建列表数据
  private listArray = []
  // 单个数据
  private listItem = {
    questionType: 2,
    options: [],
    questionTitle: '',
    subjectId: '',
    subjectNo: '',
    relateType: '1', // 1依赖2互斥3关联
    relateRule: 22, // 10章节对章节 11章节对问题 12章节对选项 20问题对章节 21问题对问题 22问题对选项 30选项对章节 31选项对问题 选项对选项
    isUnion: '0', // 是否联合依赖1是（且关系）0否（或关系）
    optionNos: [],
    isIndeterminate: true,
    checkAll: false,
    isForward: '1', // 1.选择 0.没有选择
  }
  // 设置关联关系参数
  private setRelevanceParams = {
    questionId: '',
    partNo: this.qsRelevanceParam.partNo,
    subjectNo: this.qsRelevanceParam.subjectNo,
    optionNo: this.qsRelevanceParam.optionNo,
    unionType: 1, //1且0或
    groupNo: '',
    relateItem: [
      {
        rPartNo: this.qsRelevanceParam.partNo,
        rSubjectNo: this.qsRelevanceParam.subjectNo,
        rOptionNo: this.qsRelevanceParam.rOptionNo,
        relateRule: this.qsRelevanceParam.relateRule,
        isUnion: this.qsRelevanceParam.isUnion || '0',
        isForward: this.qsRelevanceParam.isForward || '1',
        relateType: this.qsRelevanceParam.relateType,
      },
    ],
  }
  // 查询关联关系参数
  private getRelevanceParams = {
    partNo: '',
    questionId: '',
    relateType: 1,
    subjectNo: '',
    optionNo: '',
  }
  // 设置或修改组的关联
  private setRelevanceGroupParams = {
    questionId: '',
    lstQuestion: [],
    partNo: '',
    unionType: 1,
    groupNo: '',
    groupName: '',
  }
  private source = ''
  private groupRelevance = {
    subjects: [],
    groupName: '',
    groupNo: '',
    partNo: '',
    questionId: '',
    unionType: '',
  }
  private loading = false

  // 全选事件
  handleCheckAllChange(val: number, listIndex: number): void {
    if (val) {
      this.listArray[listIndex].optionNos = []
      this.listArray[listIndex].options.forEach((item) => {
        this.listArray[listIndex].optionNos.push(item.optionNo)
      })
      this.listArray[listIndex].isIndeterminate = false
    } else {
      this.listArray[listIndex].isIndeterminate = true
      this.listArray[listIndex].optionNos = []
    }
  }

  // 单个选中之后，触发全选
  handleCheckedOptionChange(value, listIndex: number): void {
    const checkedCount = value.length
    this.listArray[listIndex].checkAll =
      checkedCount === this.listArray[listIndex].options.length
    this.listArray[listIndex].isIndeterminate =
      checkedCount > 0 &&
      checkedCount < this.listArray[listIndex].options.length
  }

  // 获取可关联的题目
  getQsRelevanceList(partNo?: string): void {
    ajaxNew(
      '/business/api.v1.question/qsubject/qryAll',
      {
        partNo:
          partNo ||
          this.qsRelevanceParam.partNo ||
          (this['$route'].query.partNo as string),
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          if (
            !partNo &&
            this.source !== 'group' &&
            this.source !== 'groupByHome'
          ) {
            // 当前页面的问题关联
            res.data.items.forEach((item) => {
              // 选项为空不可关联
              if (item.options.length) {
                // 非当前问题不可关联
                if (item.subjectNo !== this.itemForm.subjectNo) {
                  // 题目再之后的不可关联
                  if (item.sortShow <= this.itemForm.sortShow) {
                    if (item.questionType === 2 || item.questionType === 4) {
                      item.questionTypeName = '单选题'
                      this.qsList.push(item)
                    } else if (
                      item.questionType === 3 ||
                      item.questionType === 5
                    ) {
                      item.questionTypeName = '多选题'
                      this.qsList.push(item)
                    }
                  }
                }
              }
            })
          } else {
            // 9宫格关联首页问题
            res.data.items.forEach((item) => {
              if (item.questionType === 2 || item.questionType === 4) {
                item.questionTypeName = '单选题'
                this.qsList.push(item)
              } else if (item.questionType === 3 || item.questionType === 5) {
                item.questionTypeName = '多选题'
                this.qsList.push(item)
              }
            })
          }
          this.qsTotal = this.qsList.length
          this.formatQsList(this.qsList)
          if (this.source === 'group' || this.source === 'groupByHome') {
            this.qryRelevanceGroup()
          } else {
            this.qryRelevance()
          }
        } else {
          this['$message'].error(res.resultMsg)
          this.loading = false
        }
        this.qsList = JSON.parse(JSON.stringify(this.qsList))
      })
      .catch((e) => {
        this['$message'].error(e)
        this.loading = false
      })
  }

  // 获取问题对应的ID查询首页题目
  qryByPosition(): void {
    qryByPositionAction({
      position: 'first',
      questionId: this.setRelevanceParams.questionId,
    }).then((res) => {
      // 12宫格需要拼接首页问题
      // fix bug TIC-CENTER-WEB-2
      res && res.length && res.forEach((item) => {
        item.questionTitle = '(首页)' + item.questionTitle
        if (item.questionType === 2 || item.questionType === 4) {
          item.questionTypeName = '单选题'
          this.qsList.push(item)
        } else if (item.questionType === 3 || item.questionType === 5) {
          item.questionTypeName = '多选题'
          this.qsList.push(item)
        }
      })
      this.qsList = JSON.parse(JSON.stringify(this.qsList))
    })
  }

  // 获取第一章节的题目
  qryByQuestion(questionId: number | string): void {
    ajaxNew(
      '/business/api.v1.question/qpart/qryByQuestion',
      {
        questionId,
        position: 'first',
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.getQsRelevanceList(res.data.items[0].partNo)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 添加关联关系
  setRelevance() {
    // 提交前格式化参数
    const relateItems = []
    this.listArray.forEach((listItem) => {
      listItem.optionNos.forEach((item) => {
        const relateItem = {
          rPartNo: '',
          rSubjectNo: '',
          rOptionNo: '',
          relateType: '1',
          relateRule: '', // 1依赖，2互斥，3关联
          isForward: '1',
          isUnion: '1', // 是否联合依赖1是（且关系）0否（或关系）
        }
        relateItem.rOptionNo = item
        relateItem.rPartNo = listItem.partNo
        relateItem.rSubjectNo = listItem.subjectNo
        relateItem.relateType = listItem.relateType
        relateItem.relateRule = listItem.relateRule
        relateItem.isUnion = listItem.isUnion
        relateItem.isForward = listItem.isForward
        relateItems.push(relateItem)
      })
    })
    this.setRelevanceParams.relateItem = relateItems
    this.loading = true
    if (this.source !== 'group' && this.source !== 'groupByHome') {
      ajaxNew(
        '/business/api.v1.question/qrelate/set',
        this.setRelevanceParams,
        'post'
      )
        .then((res) => {
          if (res.resultCode === '0') {
            this['$message']({
              message: '设置成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.emitParentFun({
                  optionIndex: this.qsRelevanceParam.optionIndex,
                })
                this.setQsListRefresh(true)
              },
            })
          } else {
            this['$message'].error(res.resultMsg)
          }
          this.loading = false
        })
        .catch((e) => {
          this['$message'].error(e)
          this.loading = false
        })
    } else {
      this.setRelevanceGroup()
    }
  }

  // 根据章节查询关联关系
  qryRelevance() {
    ajaxNew(
      '/business/api.v1.question/qrelate/qryRelateByPart',
      this.getRelevanceParams,
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const thisItem = JSON.parse(JSON.stringify(this.listItem))
          this.listArray = []
          if (!res.data.items.length) {
            this.listArray.push(thisItem)
            this.setRelevanceParams.groupNo = ''
            this.setRelevanceParams.unionType = 1
          } else {
            this.setRelevanceParams.groupNo = res.data.items[0].groupNo
            this.setRelevanceParams.unionType = res.data.items[0].unionType
            const relateItem = res.data.items[0].relateItem
            // 设置选中问题
            relateItem.forEach((rItem) => {
              this.qsList.forEach((qsItem) => {
                // 内部创建对象，防止循环之后，插入的全部是同一个值
                let obj = {}
                if (rItem.rsubjectNo === qsItem.subjectNo) {
                  thisItem.optionNos = rItem.roptionNo.split(',')
                  thisItem.subjectNo = rItem.rsubjectNo
                  thisItem.isUnion = String(rItem.isUnion)
                  thisItem.isForward = String(rItem.isForward)
                  obj = { ...thisItem, ...qsItem }
                  this.listArray.push(obj)
                }
              })
            })
          }
          this.loading = false
        } else {
          this['$message'].error(res.resultMsg)
          this.loading = false
        }
        this.qsList = JSON.parse(JSON.stringify(this.qsList))
      })
      .catch((e) => {
        this['$message'].error(e)
        this.loading = false
      })
  }

  // 删除关联关系
  delRelevance() {
    this['$confirm'](`您确认删除？`)
      .then(() => {
        this.loading = true
        if (this.source !== 'group' && this.source !== 'groupByHome') {
          ajaxNew(
            '/business/api.v1.question/qrelate/delGroup',
            {
              groupNo: this.setRelevanceParams.groupNo,
            },
            'post'
          )
            .then((res) => {
              if (res.resultCode === '0') {
                this['$message']({
                  message: '删除成功',
                  type: 'success',
                  duration: 500,
                  onClose: () => {
                    this.emitParentFun({
                      optionIndex: this.qsRelevanceParam.optionIndex,
                    })
                    this.setQsListRefresh(true)
                  },
                })
              } else {
                this['$message'].error(res.resultMsg)
              }
            })
            .catch((e) => {
              this['$message'].error(e)
            })
        } else {
          this.delRelevanceGroup()
        }
      })
      .catch((e) => {})
  }

  // 添加一项可关联
  handleAdd() {
    const item = JSON.parse(JSON.stringify(this.listItem))
    if (this.listArray.length < this.qsTotal) {
      this.listArray.push(item)
    } else {
      this['$message'].error('已超过可关联题目数量')
    }
  }

  // 删除一项可关联
  handleDel(index: number) {
    this.listArray.splice(index, 1)
  }

  // 关联题目下拉选项
  handleChange(val: number, listIndex: number) {
    const flag = this.listArray.filter((listItem) => {
      return listItem.subjectNo === val
    })
    if (flag.length <= 1) {
      this.qsList.forEach((item) => {
        if (item.subjectNo == val) {
          this.listArray[listIndex] = Object.assign(
            this.listArray[listIndex],
            item
          )
          this.listArray[listIndex].optionNos = []
        }
      })
    } else {
      this['$message'].error('关联题目不能重复')
      this.listArray[listIndex].subjectNo = ''
    }
  }

  // 格式化渲染列表 todo
  formatQsList(list): void {
    list.forEach((list) => {
      // 过滤出父级的optionId集合
      const optionIds = []
      list.options.forEach((option) => {
        if (!option.parentOptionNo) optionIds.push(option.optionNo)
      })
      // 父级是否包含子集的集合
      const hasChildrens = []
      optionIds.forEach((opS) => {
        list.options.forEach((option) => {
          if (opS === option.parentOptionNo) {
            if (!hasChildrens.includes(opS)) hasChildrens.push(opS)
          }
        })
      })
      // 删除包含了子集的父级
      if (hasChildrens.length) {
        hasChildrens.forEach((children) => {
          list.options.forEach((option, oIndex) => {
            if (children === option.optionNo) {
              list.options.splice(oIndex, 1)
            }
          })
        })
      }
    })
  }

  // 添加组关联
  setRelevanceGroup() {
    if (!this.setRelevanceGroupParams.groupName) {
      this['$message'].error('请输入规则名称')
      this.loading = false
    } else {
      this.setRelevanceGroupParams.partNo = this['$route'].query
        .partNo as string
      this.setRelevanceGroupParams.questionId = this['$route'].query
        .id as string
      this.setRelevanceGroupParams.unionType =
        this.setRelevanceParams.unionType = Number(
          this.setRelevanceParams.unionType
        )
      this.setRelevanceGroupParams.lstQuestion = []

      this.listArray.forEach((v) => {
        this.setRelevanceGroupParams.lstQuestion.push({
          optionNo: v.optionNos.join(','),
          partNo: v.partNo,
          questionId: this.setRelevanceGroupParams.questionId, //使用问卷ID
          subjectNo: v.subjectNo,
          unionType: Number(v.isUnion),
        })
      })
      this.loading = true
      if (
        this.listArray.filter((v) => {
          return v.subjectNo
        }).length
      ) {
        ajaxNew(
          '/business/api.v1.question/qitem/relateGroup',
          this.setRelevanceGroupParams,
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this['$message']({
                message: '设置成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.emitParentFun({
                    refresh: true,
                  })
                },
              })
            } else {
              this['$message'].error(res.resultMsg)
            }
            this.loading = false
          })
          .catch((e) => {
            this['$message'].error(e)
            this.loading = false
          })
      } else {
        this['$message'].error('请选择规则')
        this.loading = false
      }
    }
  }
  // 删除组关联
  delRelevanceGroup() {
    this.loading = true
    ajaxNew(
      '/business/api.v1.question/qitem/unrelateGroup',
      {
        groupNo: this.groupRelevance.groupNo,
        questionId: this.groupRelevance.questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            message: '删除成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.emitParentFun({
                refresh: true,
              })
            },
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
        this.loading = false
      })
      .catch((e) => {
        this['$message'].error(e)
        this.loading = false
      })
  }
  // 获取组关联关系
  qryRelevanceGroup() {
    const thisItem = JSON.parse(JSON.stringify(this.listItem))
    this.listArray = []
    if (this.groupRelevance && !this.groupRelevance.groupNo) {
      this.listArray.push(thisItem)
      this.setRelevanceGroupParams.groupNo = ''
      this.setRelevanceGroupParams.unionType =
        this.setRelevanceParams.unionType = 1
      this.setRelevanceGroupParams.groupName = ''
    } else {
      this.setRelevanceGroupParams.groupNo = this.groupRelevance.groupNo
      this.setRelevanceGroupParams.unionType =
        this.setRelevanceParams.unionType = Number(
          this.groupRelevance.unionType
        )
      this.setRelevanceGroupParams.questionId = this.groupRelevance.questionId
      this.setRelevanceGroupParams.groupName = this.groupRelevance.groupName

      const relateItem = this.groupRelevance.subjects
      if (relateItem.length) {
        // 设置选中问题
        relateItem.forEach((rItem) => {
          this.qsList.forEach((qsItem) => {
            // 内部创建对象，防止循环之后，插入的全部是同一个值
            let obj = {}
            if (rItem.subjectNo === qsItem.subjectNo) {
              thisItem.optionNos = rItem.optionNo.split(';')
              thisItem.subjectNo = rItem.subjectNo
              thisItem.isUnion = String(rItem.unionType)
              thisItem.isForward = 1
              obj = { ...thisItem, ...qsItem }
              this.listArray.push(obj)
            }
          })
        })
      } else {
        /* 这里不能插入空对象，插入之后第一到底选择无效 */
        this.listArray.push(thisItem)
      }
    }
    this.loading = false
  }

  mounted() {}
}
export default QsRelevance
</script>

<style>
.relevance_checkbox .el-checkbox {
  width: 100%;
}
.relevance .el-dialog__body {
  display: flex;
}
</style>>