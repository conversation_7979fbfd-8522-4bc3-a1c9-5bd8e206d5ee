<template>
  <div>
    <el-tree :data="buList" node-key="buId" :expand-on-click-node="false">
      <!--  default-expand-all 展开全部参数 -->
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <div>
          <em :style="{
            color: data.labName ? '#67C23A' : data.configName ? '#E6A23C' : '#F56C6C',
          }">{{ prefix(data) }}</em>
          {{ data.buName || data.configName || data.labName }}
        </div>
        <div>
          <el-dropdown trigger="click" v-if="from === 'bu' && !data.configName && !data.labName">
            <el-button type="primary" size="small">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="from === 'bu' && !data.configName && !data.labName"
                @click.native="handleEditBu(data)">编辑</el-dropdown-item>
              <el-dropdown-item v-if="from === 'bu' && data.parentCode === '0'"
                @click.native="handleCreateSubBu(data)">添加SubBu</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown trigger="click" v-if="from === 'businessLine' && !data.labName">
            <el-button type="primary" size="small">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="
                from === 'businessLine' && !data.configName && !data.labName
              " @click.native="handleCreateBusiness(node, data)">添加业务线</el-dropdown-item>
              <el-dropdown-item v-if="from === 'businessLine' && data.configName"
                @click.native="handleEditBusiness(node, data)">编辑业务线</el-dropdown-item>
              <!-- <el-dropdown-item v-if="from === 'businessLine' && data.configName"
                                @click.native="handleDelBusiness(data)">删除业务线</el-dropdown-item> -->
              <el-dropdown-item v-if="from === 'businessLine' && data.configName"
                @click.native="handleAddtionSample(node, data)">样品字段</el-dropdown-item>
              <el-dropdown-item v-if="from === 'businessLine' && data.configName"
                @click.native="handleSampleClassify(node, data)">样品分类</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown trigger="click" v-if="from === 'laboratory' && (data.configName || data.labName)">
            <el-button type="primary" size="small">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="from === 'laboratory' && data.configName"
                @click.native="handleCreateLaboratory(node, data)">添加实验室</el-dropdown-item>
              <el-dropdown-item v-if="from === 'laboratory' && data.labName"
                @click.native="handleEditLaboratory(node, data)">编辑实验室</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </el-tree>
    <Rname :system='system' />
    <CreateSubBu :system='system' />
    <BusinessDialog :system='system' :props="businessProps" />
    <LaboratoryDialog :system='system' :props="laboratoryProps" />
    <el-dialog title="请选择需要的样品信息字段并设置排序" :visible.sync="sampleItem.visible" width="60%" class="sample">
      <el-row>
        <el-checkbox-group v-model="checkList">
          <Draggable v-model="sampleItem.datas">
            <el-col :span="8" v-for='(item, index) of sampleItem.datas' :key='index'>
              <el-checkbox :label="item.sampleKey">{{ item.sampleKeyName }}</el-checkbox>
            </el-col>
          </Draggable>
        </el-checkbox-group>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="handleSampleCancle">取 消</el-button>
        <el-button type="primary" @click="handleSampleSubmit">确 定</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { qryBuInfo, qryByBusi, modSample, qryEnum, qrySampleKey } from './../api/common'
import Rname from './config/bu/Rname.vue' // bu改名
import CreateSubBu from './config/bu/CreateSubBu.vue' // 创建subbu
import BusinessDialog from './config/business/Dialog.vue' // 创建、修改业务线
import LaboratoryDialog from './config/laboratory/Dialog.vue' // 创建、修改实验室
import { ajaxNew } from './../api/index'
import Draggable from 'vuedraggable'

@Component({
  name: 'ConfigTree',
  components: {
    Rname,
    CreateSubBu,
    BusinessDialog,
    LaboratoryDialog,
    Draggable,
  },
})
class ConfigTree extends Vue {
  @Prop() private from
  @Prop() private system
  // 监听vuxe刷新动作，成功后重新获取列表
  @Watch('qryBuState', { immediate: true, deep: true })
  watchQryBuState(nVal) {
    if (nVal.refresh) this.getBuInfoList()
  }
  @Mutation('setQryBu') setQryBu
  @Mutation('setSubBu') setSubBu
  @Mutation('setRenameBu') setRenameBu

  private buList = []
  private rnameProps = {} // 改名组件的参数
  private businessProps = {} // 业务线组件的参数
  private laboratoryProps = {} // 业务线组件的参数

  // BU列表查询（包含subbu,businesslIne、laboratory）
  getBuInfoList(): void {
    console.log('system', this.system)
    qryBuInfo({ state: 1, system: this.system }).then((res) => {
      const parent = res.filter((v) => {
        return v.parentCode === '0'
      })
      const child = res.filter((v) => {
        return v.parentCode !== '0'
      })
      parent.forEach((parent) => {
        child.forEach((child) => {
          if (child.parentCode === parent.bu) {
            parent.children = [...parent.children, child]
            return
          }
        })
      })
      this.buList = parent
      // 获取列表成功后重置刷新状态
      this.setQryBu({
        refresh: false,
      })
    })
  }

  append(data) {
    let id = 1000
    const newChild = { id: id++, label: 'testtest', children: [] }
    if (!data.children) {
      this.$set(data, 'children', [])
    }
    data.children.push(newChild)
  }

  remove(node, data) {
    const parent = node.parent
    const children = parent.data.children || parent.data
    const index = children.findIndex((d) => d.id === data.id)
    children.splice(index, 1)
  }

  // 动态生成名称前的前缀
  prefix(data) {
    if (data.labName) {
      return '[实验室]'
    } else if (data.configName) {
      return '[业务线]'
    } else if (data.parentCode !== '0') {
      return '[sub-bu]'
    }
  }
  // 编辑bu
  handleEditBu(data): void {
    this.setRenameBu({
      visible: true,
      data,
    })
  }
  // 创建sub bu
  handleCreateSubBu(data): void {
    this.setSubBu({
      visible: true,
      data,
    })
  }
  // 编辑business
  handleCreateBusiness(node, data): void {
    this.businessProps = {
      node,
      data,
    }
  }
  // 编辑business
  handleEditBusiness(node, data): void {
    console.log(node, data)
    this.businessProps = {
      node,
      data,
    }
  }
  // 动态样品字段
  private sampleItem = {
    visible: false,
    datas: [],
  }
  private defaultCheckList = []
  private checkList = []
  private businessId = ''
  private disableGroups = []
  // 添加样品字段
  handleAddtionSample(node, data): void {
    this.businessId = data.configId
    this.checkList = JSON.parse(JSON.stringify(this.defaultCheckList))
    qryByBusi({ businessType: 'LINE', businessId: data.configId }).then(
      (res) => {
        if (res.length) {
          res.forEach((v) => {
            this.checkList.push(v.sampleKey)
          })
        }
        this.sampleItem.visible = true
      }
    )
  }

  // 样品分类按钮点击事件
    handleSampleClassify(node, data): void {
      const buId = data.bu || data.buId
      const lineId = data.configId
      const configId = data.configId
      const configName = data.configName // 新增：获取业务线名称

      // 构建新窗口的URL，传递必要的参数
      const url = `/business/sampleClassify?buId=${buId}&lineId=${lineId}&configId=${configId}&configName=${encodeURIComponent(configName)}`

      // 新打开浏览器窗口
      window.open(url, '_blank')
    }
  
  handleSampleCancle() {
    this.sampleItem.visible = false
  }
  handleSampleSubmit() {
    let arr = []
    let sortShow = 0
    this.sampleItem.datas.forEach((v) => {
      if (this.checkList.includes(v.sampleKey)) {
        sortShow++
        v.businessType = 'LINE' // 业务类型
        v.businessId = this.businessId // 业务ID
        v.sortShow = sortShow // 排序
        // v.isMust = 0 // 是否必填
        // v.isMerge = 1 // 是否合并
        arr.push(v)
      }
    })
    modSample(arr).then((res) => {
      if (res) {
        this['$message'].success('保存成功')
        this.sampleItem.visible = false
      }
    })
  }
  // 删除业务线
  handleDelBusiness(data): void {
    this['$confirm'](`您确认删除？`)
      .then(() => {
        ajaxNew(
          '/business/api.v1.center/basic/del',
          { configId: data.configId },
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this.getBuInfoList()
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            this['$message'].error(e)
          })
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 创建实验室
  handleCreateLaboratory(node, data): void {
    this.laboratoryProps = {
      node,
      data,
    }
  }
  // 编辑实验室
  handleEditLaboratory(node, data): void {
    this.laboratoryProps = {
      node,
      data,
    }
  }

  get qryBuState(): void {
    return this.$store.state.qryBu
  }

  mounted() {
    this.getBuInfoList()
    qrySampleKey({}).then((res) => {
      this.sampleItem.datas = res
      // 通过枚举值获取默认选中的数据
      // res.forEach((v) => {
      //   if (v.isMust) this.defaultCheckList.push(v.sampleKey)
      // })
    })
  }
}
export default ConfigTree
</script>

<style lang='less' scope>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;

  em {
    font-style: normal;
    font-size: 12px;
    padding-right: 5px;
    color: #999;
  }
}
</style>
<style>
.el-tree-node__content {
  min-height: 40px;
}

.el-tree>.el-tree-node:nth-child(odd) {
  background: #f0f9eb;
}

.el-tree .el-tree-node .el-tree-node:nth-child(odd) {
  background: #fff;
}
</style>
