<template>
  <div style="width: 100%;">
    <el-form ref="form"
             :model="formModel"
             label-width="100px">
      <el-form-item label="磁块名称">
        <el-input v-model="itemForm.questionTitle"
                  type='textarea'
                  placeholder="请输入磁块名称.."
                  maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="名称备注">
        <el-input v-model="itemForm.memo"
                  placeholder="请输入名称备注.."
                  maxlength="250"
                  type="textarea"></el-input>
      </el-form-item>
      <el-form-item label="展示样式">
        <el-select v-model="itemForm.isShowImg">
          <el-option value="0"
                     label="无图模式">无图模式</el-option>
          <el-option value="1"
                     label="有图模式">有图模式</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="磁块排序">
        <el-input-number v-model="itemForm.sortShow"
                         :min="0"
                         label="请输入磁块排序"></el-input-number>
        <el-button type="text"
                   @click="handleCopyDialog"
                   style="float: right;">从其他列表复制项目</el-button>
      </el-form-item>
      <!-- <el-form-item label="是否热推">
        <el-switch v-model="itemForm.isHot" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>-->
      <el-form-item label="磁块项目">
        <el-table :data="itemForm.optionItem"
                  style="width: 100%">
          <el-table-column prop="optionInfo"
                           label="项目名称"
                           width="150px">
            <template slot-scope="scope">
              <div class="display: flex; ">
                <el-input v-model="scope.row.optionInfo"
                          placeholder="请输入主标题名称.."
                          maxlength="50"></el-input>
                <el-input v-model="scope.row.subOption"
                          placeholder="请输入副标题名称.."
                          maxlength="50"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="memo"
                           label="项目描述">
            <template slot-scope="scope">
              <el-input type="textarea"
                        :rows="3"
                        v-model="scope.row.memo"
                        placeholder="请输入项目描述.."
                        maxlength="250"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="iconPath"
                           label="Icon"
                           width="80">
            <template slot-scope="scope">
              <img class="card_icon"
                   style="width: 40px; height: 40px;"
                   :src="scope.row.iconPath"
                   v-if="scope.row.iconPath"
                   @click="handleUplodaImg(scope.$index, scope.row.iconPath)" />
              <i class="el-icon-picture"
                 @click="handleUplodaImg(scope.$index)"
                 v-else></i>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           width="320px">
            <template slot-scope="scope">
              <div style="display: flex; flex-direction: column; align-items: center;">
                <div>
                  <el-button type="text"
                             :disabled="!scope.row.optionNo"
                             @click="handleItemCopy(scope.row)">复制选项</el-button>
                  <el-button type="text"
                             :disabled="!scope.row.optionNo"
                             @click="handleItemRelevance()">关联逻辑</el-button>
                  <el-button type="text"
                             :disabled="!scope.row.optionNo"
                             @click="handleRelevance(scope.$index)">问题配置</el-button>
                  <el-button type="text"
                             :disabled="!scope.row.optionNo"
                             @click="handleTestItem(scope.$index)">关联项目({{scope.row.textNum}})</el-button>
                </div>
                <el-button-group>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-minus"
                             @click="handleDel(scope.$index)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-plus"
                             @click="handleAdd(scope.$index)"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-top"
                             :disabled="!scope.$index"
                             @click="handleMove(scope.$index, 'up')"></el-button>
                  <el-button type="primary"
                             circle
                             size="small"
                             icon="el-icon-bottom"
                             :disabled="scope.$index == itemForm.optionItem.length - 1"
                             @click="handleMove(scope.$index, 'down')"></el-button>
                  <el-button title="置顶"
                             type="primary"
                             circle
                             size="small"
                             icon="el-icon-upload2"
                             :disabled="!scope.$index"
                             @click="handleMove(scope.$index, 'top')"></el-button>
                  <el-button title="置底"
                             type="primary"
                             circle
                             size="small"
                             icon="el-icon-download"
                             :disabled="scope.$index == itemForm.optionItem.length - 1"
                             @click="handleMove(scope.$index, 'bottom')"></el-button>
                </el-button-group>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label>
        <el-button type="primary"
                   @click="modQuestion">保 存</el-button>
      </el-form-item>
    </el-form>
    <el-dialog title="资源上传"
               :visible.sync="uploadFileDialog.visible"
               width="500px"
               :modal-append-to-body="true"
               :append-to-body="true">
      <FileUpload :imgPath="uploadFileDialog"
                  @childSend="childSendfun" />
    </el-dialog>
    <el-dialog title="项目问题编辑"
               :visible.sync="relevanceDialog.visible"
               width="60%"
               :modal-append-to-body="true"
               :append-to-body="true">
      <CardRelevance />
    </el-dialog>
    <el-dialog title="磁块项目关联"
               :visible.sync="itemRelevanceDialog"
               width="60%"
               :modal-append-to-body="true"
               :append-to-body="true">
      <ItemRelevance :itemRelevanceParam="itemRelevanceParam"
                     @childSendItem="childSendItemfun" />
      <div slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="itemRelevanceDialog = false">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="从其他列表复制项目"
               :visible.sync="copyItem.visible"
               width="60%"
               :modal-append-to-body="true"
               :append-to-body="true">
      <div v-for='(item, index) of copyList'
           :key='index'></div>
      <el-table ref="table"
                :data="copyList"
                style="width: 100%"
                @row-click="handleClickRow"
                stripe>
        <el-table-column type="expand">
          <template slot-scope="props"
                    v-if="props.row.options.length">
            <el-form label-position="left"
                     inline
                     class="demo-table-expand"
                     v-for="(item, i) of props.row.options"
                     :key="i">
              <el-form-item>
                <span>{{ i + 1 }}、{{ item.optionInfo }}</span>
              </el-form-item>
              <el-form-item style="float: right">
                <el-button type="text"
                           @click="handleItemCopy(item, itemForm.subjectNo)">复制</el-button>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="章节名称"
                         prop="questionTitle"></el-table-column>
        <!-- <el-table-column label="BU/业务线"
                         prop="name">
          <template slot-scope="props">{{ props.row.bu }}/{{ props.row.itemName }}</template>
        </el-table-column> -->
      </el-table>

    </el-dialog>
  </div>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
import FileUpload from './../components/FileUpload.vue'
import CardRelevance from './../views/questionnaire/CardRelevance.vue'
import ItemRelevance from './../components/question/ItemRelevance.vue'
import { ajaxNew } from './../api/index'
import { qryByPositionAction } from './../api/common'

@Component({
  name: 'Card',
  components: {
    FileUpload,
    CardRelevance,
    ItemRelevance,
  },
})
class Card extends Vue {
  @Watch('getQslistRefersh', { immediate: true, deep: true })
  watchQslistRefersh(newVal) {
    if (newVal) {
      // this.itemRelevanceDialog = false;
    }
  }

  @Prop() private childParams
  @Watch('childParams', { immediate: true, deep: true })
  getChildParams(newVal) {
    console.log('当前组件为card', newVal)
    if (newVal) {
      this.itemRelevanceParam = newVal
      this.itemRelevanceParam.source = 'card'
      this.itemForm.questionTitle = newVal.questionTitle
      this.itemForm.isHot = newVal.isHot
      this.itemForm.memo = newVal.memo
      this.itemForm.optionItem = newVal.optionItem
      this.itemForm.sortShow = newVal.sortShow
      this.itemForm.isShowImg =
        newVal.isShowImg === undefined ? '0' : String(newVal.isShowImg)
      this.itemForm.subjectNo = this.item.subjectNo = newVal.subjectNo
      this.itemForm.subjectId = this.item.subjectId = newVal.subjectId

      const thisitem = JSON.parse(JSON.stringify(this.item))
      if (!newVal.options || !newVal.options.length) {
        this.itemForm.optionItem = [thisitem]
      } else {
        this.itemForm.optionItem = newVal.options
      }
      console.log('this.itemForm.optionItem')
      this.resetOptionItem()
    }
  }

  @Prop() private cardDate
  @Emit('childSend') sendCardDate(data) {
    console.log(data)
  }

  // form占位符
  private formModel = {}
  // 编辑参数
  private itemForm = {
    partNo: this.childParams.partNo,
    subjectId: this.childParams.subjectId,
    subjectNo: this.childParams.subjectNo,
    questionType: this.childParams.questionType,
    questionTitle: this.childParams.questionTitle,
    isHot: this.childParams.isHot || 0,
    sortShow: this.childParams.sortShow,
    memo: this.childParams.memo,
    optionItem: this.childParams.optionItem || [],
    options: [],
    isShowImg: '0',
  }
  // 单个问题项
  private item = {
    subjectId: '',
    subjectNo: '',
    optionInfo: '',
    iconPath: '',
    memo: '',
    relateType: 0,
  }
  // 上传文件弹窗
  private uploadFileDialog = {
    imgPath: '',
    visible: false,
    iconPath: '',
    index: 0,
  }
  // 关联问题弹窗
  private relevanceDialog = {
    visible: false,
    index: 0,
  }

  // 选项关联
  private itemRelevanceDialog = false
  private itemRelevanceParam

  // 复制12宫格
  private copyItem = {
    visible: false,
  }
  private copyList = []

  // 接收子组件的返回值
  childSendfun(res: string) {
    this.itemForm.optionItem[this.uploadFileDialog.index].iconPath = res
    this.resetOptionItem()
  }

  // 上传图片
  handleUplodaImg(index: number, iconPath?: string): void {
    this.uploadFileDialog.visible = true
    this.uploadFileDialog.imgPath = iconPath
    this.uploadFileDialog.index = index
  }

  // 子选项的相关操作
  handleDel(index: number): void {
    if (this.itemForm.optionItem.length === 1) {
      this['$message'].error('已经是最后一条了,不能在删除.')
    } else {
      this['$confirm'](`您确认删除？`)
        .then(() => {
          this.itemForm.optionItem.splice(index, 1)
        })
        .catch((e) => {
          console.log(e)
        })
    }
    this.resetOptionItem()
  }
  handleAdd(index: number): void {
    const item = JSON.parse(JSON.stringify(this.item))
    this.itemForm.optionItem.splice(index + 1, 0, item)
    this.resetOptionItem()
  }
  handleMove(index: number, arrow: string): void {
    if (arrow === 'up') {
      this.itemForm.optionItem[index] = this.itemForm.optionItem.splice(
        index - 1,
        1,
        this.itemForm.optionItem[index]
      )[0]
    } else if (arrow === 'down') {
      this.itemForm.optionItem[index] = this.itemForm.optionItem.splice(
        index + 1,
        1,
        this.itemForm.optionItem[index]
      )[0]
    } else if (arrow === 'top') {
      this.itemForm.optionItem.unshift(
        this.itemForm.optionItem.splice(index, 1)[0]
      )
    } else if (arrow === 'bottom') {
      this.itemForm.optionItem.push(
        this.itemForm.optionItem.splice(index, 1)[0]
      )
    }
    this.resetOptionItem()
  }

  // 编辑题目
  modQuestion(): void {
    if (this.itemForm.questionTitle) {
      const flag = this.itemForm.optionItem.every((item) => {
        return item.optionInfo
      })
      if (flag) {
        this.itemForm.optionItem = this.itemForm.optionItem.filter((item) => {
          return item.optionInfo
        })
        this.itemForm.optionItem.map((v, i) => {
          return (v.sortShow = i + 1)
        })
        this.sendCardDate(this.itemForm)
      } else {
        this['$message'].error('磁块项目名称不可为空。')
      }
    } else {
      this['$message'].error('磁块名称不可为空。')
    }
  }

  // 点击关联逻辑按钮
  handleItemRelevance() {
    this.itemRelevanceDialog = true
  }

  // 复制选项
  handleItemCopy(row, targetSubjectNo?): void {
    const loading = this['$loading']({
      lock: true,
      text: '选项复制中,请稍后...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const param = {
      partNo: this.itemForm.partNo,
      optionNo: row.optionNo,
      // subjectNo: this.itemForm.subjectNo,
      subjectNo: row.subjectNo,
      optionId: row.optionId,
      targetSubjectNo,
    }
    ajaxNew('/business/api.v1.question/qoption/copy', param, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.sendCardDate('copyItem')
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  //接收子组件的返回值
  childSendItemfun(res): void {
    if (res) {
      this.itemRelevanceDialog = false
    }
  }

  // 计算属性（computed）
  get getQslistRefersh(): boolean {
    return this['$store'].state.qsListRefresh
  }

  // 强制刷新数据
  resetOptionItem(): void {
    this.itemForm.optionItem = JSON.parse(
      JSON.stringify(this.itemForm.optionItem)
    )
  }

  // 问题配置
  handleRelevance(index: number): void {
    const item = this.itemForm.optionItem[index]
    const partName = encodeURIComponent(item.optionInfo) // url参数单独编码，含有%等字符报错。
    window.open(
      `cardRelevance?id=${this['$route'].query.id}&partName=${partName}&subjectNo=${this.itemForm.subjectNo}&optionNo=${item.optionNo}&partNo=${this.itemForm.partNo}`
    )
    // this["$router"].push(
    //   `cardRelevance?id=${this["$route"].query.id}&partName=${item.optionInfo}&subjectNo=${this.itemForm.subjectNo}&optionNo=${item.optionNo}&partNo=${this.itemForm.partNo}`
    // );
  }

  // 关联测试项目
  handleTestItem(index: number): void {
    const item = this.itemForm.optionItem[index]
    window.open(
      `testItem/?questionId=${this['$route'].query.id}&optionNo=${item.optionNo}&subjectNo=${this.itemForm.subjectNo}`
    )
  }

  // 复制
  handleCopyDialog(): void {
    this.copyItem.visible = true
    this.copyList = []
    this.qryByPosition()
  }
  qryByPosition(row?): void {
    console.log(row)
    const params = {
      position: 'second',
      questionId: row ? row.questionId : this['$route'].query.id,
    }
    qryByPositionAction(params).then((res) => {
      if (row) {
        row.items = res.filter((v) => {
          return v.questionType !== 6
        })
      } else {
        this.copyList = res.filter((v) => {
          return v.questionType === 3
        })
      }
    })
  }

  // 点击整行
  handleClickRow(row, column, event): void {
    const parentnNode = event.target.parentNode.parentNode.className
    const node: any = this.$refs['table']
    node.toggleRowExpansion(row, !parentnNode.includes('expanded'))
  }

  mounted(): void {}
}
export default Card
</script>

<style lang="less" scoped>
.card_icon {
  width: 20px;
  height: 20px;
}
</style>
