<!--
 * @Description: 富文本编辑器组件
 * @Author: pjy
 * @Date: 2019-08-09 19:39:13
 * @LastEditTime: 2019-10-16 19:03:18
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="uedtior">
    <div :id="id"></div>
  </div>
</template>
<script lang="ts">
import { Prop, Vue, Component, Watch, Emit } from "vue-property-decorator";

@Component({
  name: "UE",
})
export default class UE extends Vue {
  @Prop()
  config?: object;
  @Prop()
  id?: string;
  @Prop({ default: "" }) value?: string;
  editor: any = null;
  mounted() {
    this.editor = (window as any).UE.getEditor(this.id, this.config);
    this.editor.addListener("ready", () => {
      this.editor.setContent(this.value); // 确保UE加载完成后，放入内容。
      this.editor.addListener("contentChange", () => {
        this.$emit("ueditorContent", this.editor.getContent()); //内容发生变化，触发input事件，此处是为了实现v-mode功能
        this.$emit("ueditorContentText", this.editor.getContentTxt()); //内容发生变化，触发input事件
      });
    });
  }
  getUEContent() {
    return this.editor.getContent();
  }

  getUEContentText() {
    return this.editor.getContentTxt();
  }

  destroyed() {
    this.editor.destroy();
  }
}
</script>
<style lang="less" scoped>
</style>