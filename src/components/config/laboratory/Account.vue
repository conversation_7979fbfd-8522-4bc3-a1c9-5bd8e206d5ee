<template>
  <el-dialog
    title="账户信息列表"
    :visible.sync="dialogVisible"
    width="80%"
    :append-to-body="appendToBody"
    @close="dialogVisible = false"
  >
    <el-table :data="accountList" stripe style="width: 100%">
      <el-table-column width="50" class="account-radio">
        <template slot-scope="scope">
          <el-radio v-model="accountNo" :label="scope.row.accountNo"></el-radio>
        </template>
      </el-table-column>
      <el-table-column prop="accountName" label="账户名称"> </el-table-column>
      <el-table-column prop="bankAddress" label="银行地址"> </el-table-column>
      <el-table-column prop="bankBranchNo" label="银行分行号">
      </el-table-column>
      <el-table-column prop="bankName" label="银行名称"> </el-table-column>
      <el-table-column prop="bankNo" label="银行卡号"> </el-table-column>
      <el-table-column prop="bankTel" label="电话"> </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancle">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { qryAccount } from "./../../../api/common";

@Component({
  name: "Account",
})
class Account extends Vue {
  @Prop() private accountProps;
  @Watch("accountProps", { immediate: true, deep: true })
  getAccountProps(nVal) {
    if (nVal.visible) {
      this.qryAccountList();
      this.accountNo = nVal.accountNo;
      this.dialogVisible = true;
    }
  }

  private appendToBody = true;
  private dialogVisible = false;
  private accountList = [];
  private accountNo = "";

  handleCancle() {
    this.dialogVisible = false;
    this.emitAccount();
  }
  handleOk() {
    this.dialogVisible = false;
    this.emitAccount();
  }

  qryAccountList() {
    qryAccount({
      paymentMethod: '300000'
    }).then((res) => {
      this.accountList = res;
    });
  }

  emitAccount() {
    let account = {};
    if (this.accountNo)
      account = this.accountList.filter((v) => {
        return v.accountNo === this.accountNo;
      })[0];
      
    this["$emit"]("emitAccount", {
      visible: false,
      account,
    });
  }
}
export default Account;
</script>

<style>
.el-radio__label {
  display: none;
}
</style>