<template>
  <div>
    <el-tree v-if="datas.length"
             :data="datas"
             node-key="buId"
             :expand-on-click-node="false">
      <div slot-scope="{ node, data }"
           class="custom-tree-node">
        <div>
          <em :style="{
              color: data.labName
                ? '#67C23A'
                : data.configName
                ? '#E6A23C'
                : '#F56C6C',
            }">{{ prefix(data) }}</em>
          <label v-if="data.configName">
            <el-radio v-model="relateId"
                      :label="data.configId"
                      @change="() => handleChange(data.bu)"></el-radio>
          </label>
          {{ data.buName || data.configName || data.labName }}
        </div>
      </div>
    </el-tree>
    <div class="radio_group">
      <el-radio-group v-if="businessLine.length"
                      v-model="relateId"
                      @change="() => handleChange('')">
        <el-radio v-for="(v, index) of businessLine"
                  :key="index"
                  :value="v.configId"
                  :label="v.configId">{{ v.configName }}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { qryBuInfo, qryInfoByLab } from './../../../api/common'

@Component({
  name: 'BusinessLineRadio',
  components: {},
})
class BusinessLineRadio extends Vue {
  // 辅助间传入进来的bu和relateId
  @Prop() private businessLineRadioProps
  @Prop() private system
  @Watch('businessLineRadioProps', { immediate: true, deep: true })
  getPrpps(nVal) {
    // 新增的时候获取全部业务线
    // 编辑的时候获取自己bu下的业务线
    if (nVal && nVal.bu) {
      this.getInfoByLab(nVal.bu)
      this.relateId = nVal.relateId
      this.bu = nVal.bu
    } else {
      this.getBuInfoList()
      this.relateId = ''
    }
  }

  private datas = []
  private businessLine = []
  private relateId = ''
  private bu = ''

  // 查询全部业务线
  getBuInfoList(): void {
    if (!this.datas.length) {
      qryBuInfo({ system: this.system }).then((res) => {
        const parent = res.filter((v) => {
          return v.parentCode === '0'
        })
        const child = res.filter((v) => {
          return v.parentCode !== '0'
        })
        parent.forEach((parent) => {
          child.forEach((child) => {
            if (child.parentCode === parent.bu) {
              parent.children = [...parent.children, child]
            }
          })
        })
        this.datas = parent
        this.businessLine = []
      })
    }
  }

  // 获取该bu下的业务线
  getInfoByLab(bu) {
    if (!this.businessLine.length) {
      qryInfoByLab({ bu }).then((res) => {
        this.businessLine = res
        this.datas = []
      })
    }
  }

  // 动态生成名称前的前缀
  prefix(data) {
    if (data.labName) {
      return '[实验室]'
    } else if (data.configName) {
      return '[业务线]'
    } else if (data.parentCode !== '0') {
      return '[sub-bu]'
    }
  }

  // 选择了业务线
  handleChange(bu): void {
    // 这里的大小bu不同
    const params = {
      bu: bu || this.bu,
      relateId: this.relateId,
    }
    this['$emit']('emitBusinessLineId', params)
  }

  get qryLaboratory(): void {
    return this.$store.state.laboratory
  }
}
export default BusinessLineRadio
</script>

<style lang='less' scope>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;

  em {
    font-style: normal;
    font-size: 12px;
    padding-right: 5px;
    color: #999;
  }
}
.radio_group {
  .el-radio {
    display: block;
    margin-top: 10px;
  }
  .el-radio__label {
    display: inline-block;
  }
}
</style>