<template>
  <el-dialog
    title="支付宝账号"
    :visible.sync="dialogVisible"
    width="80%"
    :append-to-body="appendToBody"
    @close="dialogVisible = false"
  >
    <el-table :data="accountList" stripe style="width: 100%">
      <el-table-column width="50" class="account-radio">
        <template slot-scope="scope">
          <el-radio v-model="accountNo" :label="scope.row.channelCode"></el-radio>
        </template>
      </el-table-column>
      <el-table-column prop="accountName" label="商户名称"> </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancle">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { qryAccount } from "../../../api/common";

@Component({
  name: "Account",
})
class Account extends Vue {
  @Prop() private accountAlipayProps;
  @Watch("accountAlipayProps", { immediate: true, deep: true })
  getAccountAlipayProps(nVal) {
    if (nVal.visible) {
      this.qryAccountList();
      this.accountNo = nVal.alipayChannel;
      this.dialogVisible = true;
    }
  }

  private appendToBody = true;
  private dialogVisible = false;
  private accountList = [];
  private accountNo = "";

  handleCancle() {
    this.dialogVisible = false;
    this.emitAccountAlipay();
  }
  handleOk() {
    this.dialogVisible = false;
    this.emitAccountAlipay();
  }

  qryAccountList() {
    qryAccount({
      paymentMethod: '100000'
    }).then((res) => {
      this.accountList = res;
    });
  }

  emitAccountAlipay() {
    let account = {};
    if (this.accountNo)
      account = this.accountList.filter((v) => {
        return v.channelCode === this.accountNo;
      })[0];
    this["$emit"]("emitAccountAlipay", {
      visible: false,
      account,
    });
  }
}
export default Account;
</script>

<style>
.el-radio__label {
  display: none;
}
</style>