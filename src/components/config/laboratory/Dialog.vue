<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="80%" @close="handleCancle('form')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form ref="form" :model="form" label-width="140px" :rules="rules">
      <el-form-item label="名称：" prop="labName">
        <el-input type="input" v-model="form.labName" placeholder="请输入实验室名称..."></el-input>
      </el-form-item>
      <el-form-item label="地址：">
        <Address @emitAddress="emitAddress" :addressProps="addressProps" />
      </el-form-item>
      <el-form-item label="联系人：">
        <el-input type="input" v-model="form.linkPerson" placeholder="请输入实验室联系人..."></el-input>
      </el-form-item>
      <el-form-item label="电话：">
        <el-input type="input" v-model="form.linkPhone" placeholder="请输入实验室电话..."></el-input>
      </el-form-item>
      <el-form-item label="邮编：">
        <el-input type="input" v-model="form.postcode" placeholder="请输入实验室邮编..."></el-input>
      </el-form-item>
      <el-form-item label="LabCode：" prop='labCode'>
        <el-select style="width: 100%;" v-model="form.labCode" placeholder="请选择labCode" filterable>
          <el-option v-for="(item, index) of labCodeList" :label="item.enumName" :key="index"
            :value="item.enumCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账号渠道：" prop='paymentChannelSource'>
        <el-radio-group v-model="form.paymentChannelSource">
          <el-radio v-for="(item, index) of paymentChannelSourceList" :key="index" :value="item.enumCode"
            :label="item.enumCode">{{
              item.enumName }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.paymentChannelSource === 'LAB'">
        <el-form-item label="对公账号：" prop='accountNo'>
          <el-button type="primary" @click="handleAccount">{{ accountName || "选择账户" }}</el-button>
        </el-form-item>
        <el-form-item label="支付宝账号：" prop='alipayChannel'>
          <el-select style="width: 100%;" v-model="form.alipayChannel" placeholder="请选择支付宝账号">
            <el-option v-for="(item, index) of accountAlipayList" :label="item.accountName" :key="index"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="微信账号：" prop='wxpayChannel'>
          <el-select style="width: 100%;" v-model="form.wxpayChannel" placeholder="请选择微信账号">
            <el-option v-for="(item, index) of accountWxpayList" :label="item.accountName" :key="index"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="业务线：" prop='relateId'>
        <BusinessLineRadio :system="system" :businessLineRadioProps="businessLineRadioProps"
          @emitBusinessLineId="emitBusinessLineId" />
      </el-form-item>
      <el-form-item label="是否展示给客户：" prop='isShowCustomer'>
        <el-radio-group v-model="form.isShowCustomer">
          <el-radio v-for="(item, index) of isShowCustomerList" :key="index" :value="item.enumCode"
            :label="item.enumCode">{{
              item.enumName }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancle('form')">取 消</el-button>
      <el-button type="primary" @click="handleOk('form')">确 定</el-button>
    </span>
    <Account :accountProps="accountProps" @emitAccount="emitAccount" />
    <AccountAlipay :accountAlipayProps="accountAlipayProps" @emitAccountAlipay="emitAccountAlipay" />
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import {
  getLaboratory,
  addLaboratory,
  modLaboratory,
  qryByChannel,
  qryAccount,
  qryEnum
} from './../../../api/common'
import Address from './../../Address.vue'
import Account from './Account.vue'
import AccountAlipay from './AccountAlipay.vue'
import BusinessLineRadio from './BusinessLineRadio.vue'

@Component({
  name: 'LaboratoryDialog',
  components: {
    Address,
    Account,
    AccountAlipay,
    BusinessLineRadio,
  },
})
class LaboratoryDialog extends Vue {
  @Prop() private system
  // 监听vuxe刷新动作，成功后重新获取列表
  @Watch('qryLaboratory', { immediate: true, deep: false })
  watchQryLaboratory(nVal) {
    if (nVal && nVal.visible) {
      // 编辑的时候获取实验室信息
      if (nVal.data.labId) {
        this.title = '实验室修改'
        this.form = nVal.data
        this.form.relateId = nVal.data.businessLineId
        this.addressProps = {
          province: nVal.data.province,
          city: nVal.data.city,
          town: nVal.data.town,
          labAddress: nVal.data.labAddress,
        }
        this.businessLineRadioProps = {
          bu: nVal.data.bu,
          relateId: nVal.data.businessLineId,
        }
        this.accountProps = {
          accountNo: nVal.data.accountNo,
          visible: false,
        }
        this.getLaboratoryDtl(nVal.data.labId)
      } else {
        this.form = {
          bu: '', // BU
          labName: '', // 实验室名称
          labCode: '',
          labId: '', // 实验室ID
          linkPerson: '', // 联系人
          linkPhone: '', // 联系电话
          province: '', // 省
          city: '', // 市
          town: '', // 区
          labAddress: '', // 地址
          postcode: '', // 邮编
          accountNo: '', // 关联的账户ID
          relateId: '', // 业务线ID
          relateType: 1, // 1BU,2SUBBU,3LINE
          alipayChannel: '',
          wxpayChannel: '',
          belongSystem: 'OIQ',
          paymentChannelSource: 'LAB',
          isShowCustomer: 1
        }
        this.addressProps = {
          province: '',
          city: '',
          town: '',
          labAddress: '',
        }
        this.businessLineRadioProps = {
          bu: '',
          relateId: '',
        }
        this.accountProps = {
          accountNo: '',
          visible: false,
        }
        this.accountAlipayProps = {
          alipayChannel: '',
          visible: false,
        }
        this.accountName = ''
        this.accountNameAlipay = ''
      }
      this.dialogVisible = true
    }
    // 支付宝账户列表
    qryAccount({
      paymentMethod: '100000'
    }).then((res) => {
      this.accountAlipayList = [{ channelCode: 'disables', accountName: '禁用' }, ...res];
    });
    // 微信账户列表
    qryAccount({
      paymentMethod: '100001'
    }).then((res) => {
      this.accountWxpayList = [{ channelCode: 'disables', accountName: '禁用' }, ...res];
    });
    // labcode枚举值
    qryEnum({
      tableName: 'ORDER_BASE_INFO',
      colName: 'LAB_CODE',
    }).then((res) => {
      this.labCodeList = res
    })
    // 账号渠道枚举值
    qryEnum({
      tableName: 'SYS_LAB',
      colName: 'PAYMENT_CHANNEL_SOURCE',
    }).then((res) => {
      this.paymentChannelSourceList = res
    })

  }
  @Mutation('setQryBu') setQryBu
  @Mutation('setLaboratory') setLaboratory

  private accountAlipayList = [];
  private accountWxpayList = [];
  private labCodeList = [];
  private paymentChannelSourceList = []
  private form = {
    bu: '', // BU
    labName: '', // 实验室名称
    labId: '', // 实验室ID
    linkPerson: '', // 联系人
    linkPhone: '', // 联系电话
    province: '', // 省
    city: '', // 市
    town: '', // 区
    labAddress: '', // 地址
    postcode: '', // 邮编
    accountNo: '', // 关联的账户ID
    relateId: '', // 业务线ID
    relateType: 1, // 1BU,2SUBBU,3LINE
    alipayChannel: '',
    wxpayChannel: '',
    labCode: '',
    belongSystem: 'OIQ',
    paymentChannelSource: 'LAB',
    isShowCustomer: 1
  }
  private dialogVisible = false
  private title = '实验室新增'
  private buName = ''
  private accountName = ''
  private accountNameAlipay = ''
  private accountNameWxpay = ''
  private addressProps = {} // 传递给地址组件的值
  // 传递给业务线选择组件的值
  private businessLineRadioProps = {
    bu: '',
    relateId: '',
  }
  // 传递给账户选择组件的值
  private accountProps = {
    accountNo: '',
    visible: false,
  }
  // 传递给账户选择组件的值
  private accountAlipayProps = {
    alipayChannel: '',
    visible: false,
  }
  // 必填校验
  private rules = {
    labName: [{ required: true, message: '请输入实验室名称', trigger: 'blur' }],
    relateId: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
    accountNo: [{ required: true, message: '请选择对公账号', trigger: 'blur' }],
    alipayChannel: [{ required: true, message: '请选择支付宝账号', trigger: 'blur' }],
    wxpayChannel: [{ required: true, message: '请选择微信账号', trigger: 'blur' }],
    labCode: [{ required: true, message: '请选择labCode', trigger: 'blur' }],
    paymentChannelSource: [{ required: true, message: '请选择账号渠道', trigger: 'blur' }],
    isShowCustomer: [{ required: true, message: '请选择是否展示给客户', trigger: 'blur' }],
  }
  // 是否展示给客户
  private isShowCustomerList = [
    {
      enumCode: 1,
      enumName: '是'
    },
    {
      enumCode: 0,
      enumName: '否'
    },
  ]

  handleCancle(formName: string): void {
    // 清空表单验证
    const ref: any = this.$refs[formName]
    ref.clearValidate()
    this.setState(false)
    window.location.reload()
  }

  // 选取 labcode
  handleLabCode() {
    this.accountProps = {
      accountNo: this.accountProps.accountNo,
      visible: true,
    }
  }
  handleAccount() {
    this.accountProps = {
      accountNo: this.accountProps.accountNo,
      visible: true,
    }
  }

  handleAlipay() {
    this.accountAlipayProps = {
      alipayChannel: this.accountAlipayProps.alipayChannel,
      visible: true,
    }
  }

  // 选择微信支付
  handleWxPay() {
    this.accountAlipayProps = {
      alipayChannel: this.accountAlipayProps.alipayChannel,
      visible: true,
    }
  }


  // 创建、修改实验室
  handleOk(formName: string): void {
    const ref: any = this.$refs[formName]
    console.log(this.form, 'aaaaaaaaaaaaaaa')
    ref.validate((valid) => {
      if (valid) {
        /* 账号渠道为执行系统回传时，入参做优化 */
        if (this.form.paymentChannelSource === 'OTHER') {
          delete this.form.accountNo
          delete this.form.alipayChannel
          delete this.form.wxpayChannel
          // delete this.form.relateId
        }
        if (!this.form.labId) {
          delete this.form.labId
          addLaboratory(this.form).then((res) => {
            if (res) {
              this.setState(true)
              window.location.reload()
            }
          })
        } else {
          modLaboratory(this.form).then((res) => {
            if (res) {
              this.setState(true)
              window.location.reload()
            }
          })
        }
      } else {
        return false
      }
    })
  }

  // 获取实验室详情
  getLaboratoryDtl(labId) {
    getLaboratory({ labId }).then((res) => {
      this.accountName = res.accountName
      this.form.labCode = res.labCode
      this.form.belongSystem = res.belongSystem
      this.form.isShowCustomer = res.isShowCustomer
      this.form.paymentChannelSource = res.paymentChannelSource
      this.form.alipayChannel = res.alipayChannel
      this.form.wxpayChannel = res.wxpayChannel

      if (res.alipayChannel) {
        qryByChannel({ channelCode: res.alipayChannel }).then((res1) => {
          if (res1) {
            this.accountNameAlipay = res1.accountName
          } else {
            this.accountNameAlipay = ''
          }
        })
      } else {
        this.accountNameAlipay = ''
      }

      this.accountAlipayProps = {
        alipayChannel: res.alipayChannel,
        visible: false,
      }
    })
  }

  setState(type) {
    this.dialogVisible = false
    this.setLaboratory({
      visible: false,
      data: {},
    })
    this.setQryBu({
      refresh: type,
    })
  }

  // 监听地址组件返回的地址信息
  emitAddress(val): void {
    this.form = Object.assign(this.form, val)
  }

  // 监听账户信息返回值
  emitAccount(val): void {
    this.accountProps = {
      accountNo: this.accountProps.accountNo,
      visible: false,
    }
    this.form.accountNo = val.account.accountNo
    this.accountName = val.account.accountName
  }

  // 监听支付账户信息返回值
  emitAccountAlipay(val): void {
    this.accountAlipayProps = {
      alipayChannel: this.accountAlipayProps.alipayChannel,
      visible: false,
    }
    this.form.alipayChannel = val.account.channelCode
    this.accountNameAlipay = val.account.accountName
  }

  // 接收选中的业务线id
  emitBusinessLineId(val): void {
    debugger
    this.form = Object.assign(this.form, val)
  }

  get qryLaboratory(): void {
    return this.$store.state.laboratory
  }
}
export default LaboratoryDialog
</script>


<style>
.el-radio__label {
  display: inline-block !important;
}
</style>