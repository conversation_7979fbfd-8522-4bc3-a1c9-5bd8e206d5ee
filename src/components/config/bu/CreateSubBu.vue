<template>
  <el-dialog title="创建SubBu"
             :visible.sync="dialogVisible"
             width="30%">
    <el-form ref="form"
             :model="form"
             label-width="110px"
             @submit.native.prevent>
      <el-form-item label="SubBu名称：">
        <el-input type="input"
                  v-model="form.buName"></el-input>
      </el-form-item>
      <!-- <el-form-item label="BUCode：">
        <el-input type="input" v-model="form.bu"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="ParentCode：">
        <el-input type="input" v-model="props.parentCode" disabled></el-input>
      </el-form-item> -->
    </el-form>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="handleCancle">取 消</el-button>
      <el-button type="primary"
                 @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { addSubBu } from './../../../api/common'

@Component({
  name: 'CreateSubBu',
})
class CreateSubBu extends Vue {
  @Prop() private system
  @Watch('subBuState', { immediate: true, deep: true })
  watchSubBuState(nVal) {
    if (nVal && nVal.visible) {
      this.dialogVisible = true
      this.form.parentCode = nVal.data.bu
      this.form.buName = ''
    }
  }
  @Mutation('setQryBu') setQryBu
  @Mutation('setSubBu') setSubBu

  private form = {
    buName: '',
    parentCode: '',
    system: this.system,
  }
  private dialogVisible = false

  handleCancle() {
    this.dialogVisible = false
    this.setSubBu({
      visibale: false,
      data: {},
    })
  }

  // 创建subBu
  handleOk() {
    if (!this.form.buName) {
      this['$message']({
        message: 'SubBu名称不能为空。',
        type: 'error',
      })
      return false
    }
    addSubBu(this.form).then((res) => {
      if (res) {
        this.dialogVisible = false
        this.setQryBu({
          refresh: true,
        })
        this.setSubBu({
          visibale: false,
          data: {},
        })
      }
    })
  }

  get subBuState(): void {
    return this.$store.state.subBu
  }
}
export default CreateSubBu
</script>