<template>
  <el-dialog title="编辑BU"
             :visible.sync="dialogVisible"
             width="30%">
    <el-form ref="form"
             :model="form"
             label-width="90px"
             @submit.native.prevent>
      <el-form-item label="BU名称：">
        <el-input type="input"
                  v-model="form.buName"></el-input>
      </el-form-item>
      <el-form-item label="BUCode：">
        <el-input type="input"
                  v-model="form.bu"
                  disabled></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="handleCancle">取 消</el-button>
      <el-button type="primary"
                 @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { rnameBu } from './../../../api/common'

@Component({
  name: 'Rname',
})
class Rname extends Vue {
  @Prop() private system
  @Watch('renameBuState', { immediate: true, deep: true })
  watchRenameBuState(nVal) {
    if (nVal && nVal.visible) {
      this.form.buName = nVal.data.buName
      this.form.bu = nVal.data.bu
      this.dialogVisible = true
    }
  }
  @Mutation('setQryBu') setQryBu
  @Mutation('setRenameBu') setRenameBu

  private form = {
    buName: '',
    bu: '',
    system: this.system,
  }
  private dialogVisible = false

  handleCancle() {
    this.dialogVisible = false
    this.setRenameBu({
      visibale: false,
      data: {},
    })
  }

  // 修改bu名称
  handleOk() {
    if (!this.form.buName) {
      this['$message']({
        message: 'BU名称不能为空。',
        type: 'error',
      })
      return false
    }
    rnameBu(this.form).then((res) => {
      if (res) this.dialogVisible = false
      this.setQryBu({
        refresh: true,
      })
      this.setRenameBu({
        visibale: false,
        data: {},
      })
    })
  }

  mounted() {
    console.log('this.system', this.system)
  }

  get renameBuState(): void {
    return this.$store.state.renameBu
  }
}
export default Rname
</script>