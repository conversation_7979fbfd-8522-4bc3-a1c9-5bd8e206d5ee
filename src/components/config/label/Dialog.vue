<template>
  <el-dialog title="标签配置"
             :visible.sync="dialogVisible"
             width="80%"
             @close="handleCancle('form')"
             :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form ref="form"
             :model="form"
             label-width="140px"
             :rules="rules">
      <el-form-item label="标签名称："
                    prop="labelName">
        <el-input type="input"
                  v-model="form.labelName"
                  placeholder="请输入标签名称..."
                  maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="描述："
                    prop="labelDesc">
        <el-input type="textarea"
                  v-model="form.labelDesc"
                  placeholder="请输入描述..."
                  maxlength="255"></el-input>
      </el-form-item>
      <el-form-item label="客户端提示文案："
                    prop="fillPrompt">
        <el-input v-model="form.fillPrompt"
                  placeholder="请输入客户端提示文案..."
                  maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="是否可编辑：">
        <el-radio-group v-model="form.isEdit">
          <el-radio v-for="(item, index) of enumList"
                    :key="index"
                    :value="item.enumCode"
                    :label="item.enumCode"
                    disabled>{{
              item.enumName }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="选项："
                    class="question_eidt"
                    prop="labelOptionList">
        <div class="item-title">
          <div class="item-optionInfo">选项文字</div>
          <div class="item-isFill">允许填空</div>
          <div class="item-action">操作</div>
        </div>
        <div class="item-list"
             v-for="(parentItem, parentIndex) of form.labelOptionList"
             :key="parentIndex">
          <div class="item-parent">
            <div class="item-optionInfo">
              <el-input placeholder="请输入文字..."
                        maxlength="100"
                        v-model="parentItem.optionName"
                        type="textarea"></el-input>
            </div>
            <div class="item-isFill">
              <el-checkbox v-model="parentItem.isFill"
                           :true-label="1"
                           :false-label="0"></el-checkbox>
            </div>
            <div class="item-action">
              <el-button-group>
                <el-button type="primary"
                           circle
                           size="small"
                           icon="el-icon-minus"
                           @click="handleDel(parentIndex)"></el-button>
                <el-button type="primary"
                           circle
                           size="small"
                           icon="el-icon-plus"
                           @click="handleAdd(parentIndex)"></el-button>
                <el-button type="primary"
                           circle
                           size="small"
                           icon="el-icon-top"
                           :disabled="!parentIndex"
                           @click="handleMove(parentIndex, 'up')"></el-button>
                <el-button type="primary"
                           circle
                           size="small"
                           icon="el-icon-bottom"
                           :disabled="parentIndex == form.labelOptionList.length - 1"
                           @click="handleMove(parentIndex, 'down')"></el-button>
              </el-button-group>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="handleCancle('form')">取 消</el-button>
      <el-button type="primary"
                 @click="handleOk('form')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { addTag, editTag } from './../../../api/common'

@Component({
  name: 'LabelDialog',
  components: {},
})
class LabelDialog extends Vue {
  @Prop() private system
  // 监听vuxe刷新动作，成功后重新获取列表
  get qryLabel(): void {
    return this.$store.state.label
  }

  @Watch('qryLabel', { immediate: true, deep: false })
  watchQryLabel(nVal) {
    console.log(nVal, '--qryLabel--')
    this.dialogVisible = nVal.visible
    if (nVal.data && nVal.data.labelId) {
      this.form = { ...this.form, ...nVal.data }
    }
    // 如果选项为空，插入一条空
    if (!this.form.labelOptionList.length) {
      this.form.labelOptionList.push({
        optionName: '',
        isFill: 0,
        sortShow: 0,
      })
    }
    console.log(this.form, '--this.form--')
  }
  @Mutation('setQryBu') setQryBu
  @Mutation('setLabel') setLabel

  private form = {
    businessType: 'category',
    businessCode: 'all',
    isEdit: 1,
    labelOptionList: [],
  }
  private dialogVisible = false
  // 必填校验
  private rules = {
    labelName: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
    labelOptionList: [
      { required: true, message: '请添加选项', trigger: 'blur' },
    ],
  }
  // 是否可编辑
  private enumList = [
    {
      enumCode: 1,
      enumName: '是',
    },
    {
      enumCode: 0,
      enumName: '否',
    },
  ]
  handleDel(parentIndex: number) {
    // 删除选项， 仅有一条禁止删除
    if (this.form.labelOptionList.length === 1) {
      this.$message({
        type: 'warning',
        message: '至少需要一个选项',
      })
      return
    }
    this.form.labelOptionList.splice(parentIndex, 1)
  }
  handleAdd(parentIndex: number) {
    this.form.labelOptionList.push({
      optionName: '',
      isFill: 0,
      sortShow: 0,
    })
  }
  handleMove(parentIndex: number, arrow: string) {
    const list = this.form.labelOptionList
    if (arrow === 'up' && parentIndex > 0) {
      // 上移：交换当前项和上一项
      ;[list[parentIndex - 1], list[parentIndex]] = [
        list[parentIndex],
        list[parentIndex - 1],
      ]
    }
    if (arrow === 'down' && parentIndex < list.length - 1) {
      // 下移：交换当前项和下一项
      ;[list[parentIndex], list[parentIndex + 1]] = [
        list[parentIndex + 1],
        list[parentIndex],
      ]
    }
    this.form.labelOptionList = JSON.parse(JSON.stringify(list))
  }

  handleCancle(formName: string): void {
    // 清空表单验证
    const ref: any = this.$refs[formName]
    ref.clearValidate()
    this.setState()
  }

  // 创建、修改实验室
  handleOk(formName: string): void {
    const ref: any = this.$refs[formName]
    ref.validate((valid) => {
      if (valid) {
        // 校验选项是否每条都有文字
        const invalidOption = this.form.labelOptionList.some(
          (item) => !item.optionName.trim()
        )
        if (invalidOption) {
          this.$message({
            type: 'warning',
            message: '每个选项都需要填写文字',
          })
          return
        }

        // 选项sortShow排序
        this.form.labelOptionList.forEach((item, index) => {
          item.sortShow = index + 1
        })
        // 根据labelId判断是添加还是修改
        if (this.form.labelId) {
          // 修改标签
          editTag(this.form).then((res) => {
            if (res) this.setState()
          })
        } else {
          // 添加标
          addTag(this.form).then((res) => {
            if (res) this.setState()
          })
        }
      } else {
        return false
      }
    })
  }

  setState() {
    window.location.reload()
  }

  // 接收选中的业务线id
  emitBusinessLineId(val): void {
    this.form = Object.assign(this.form, val)
  }
}
export default LabelDialog
</script>


<style scoped lang="less">
.question_eidt {
  border: none;
}
</style>