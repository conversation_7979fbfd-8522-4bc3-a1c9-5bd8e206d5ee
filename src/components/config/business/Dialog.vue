<template>
  <el-dialog :title="title"
             :visible.sync="dialogVisible"
             width="30%">
    <el-form ref="form"
             :model="form"
             label-width="200px"
             @submit.native.prevent>
      <el-form-item label="业务线名称：">
        <el-input type="input"
                  v-model="form.configName"
                  placeholder="请输入业务线名称"></el-input>
      </el-form-item>
      <el-form-item label="OIQ询价单自动关闭时间：" v-if="form.system === 'ticCenter'">
        <el-input-number style="width: 100%;"
                         v-model="form.closeDays"
                         :min="7"
                         :max="100"
                         placeholder="请输入7-100的数字"></el-input-number>
      </el-form-item>
      <el-form-item label="BU：">
        {{ buName }}
      </el-form-item>
    </el-form>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="handleCancle">取 消</el-button>
      <el-button type="primary"
                 @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { addBusiness, modBusiness } from './../../../api/common'

@Component({
  name: 'BusinessDialog',
})
class BusinessDialog extends Vue {
  @Prop() private props
  @Prop() private system
  @Watch('props')
  getProps(nVal) {
    if (nVal && nVal.data.bu) {
      this.form.bu = nVal.data.bu
      this.form.configId = nVal.data.configId
      this.form.configName = nVal.data.configName
      this.form.closeDays = nVal.data.closeDays || 7
      this.title = nVal.data.configId ? '业务线修改' : '业务线新增'
      this.dialogVisible = true
      // debugger
      if (nVal.node.level === 1) {
        this.buName = nVal.data.buName
      } else if (nVal.node.level === 2) {
        this.buName = nVal.data.buName || nVal.node.parent.data.buName
      } else {
        this.buName =
          nVal.data.buName || nVal.node.parent.data.parent.data.buName
      }
      console.log(nVal)
      // this.buName = nVal.data.buName
    }
  }
  @Mutation('setQryBu') setQryBu

  private form = {
    bu: '',
    configType: 100000, // 业务线
    configName: '', // 业务线名称
    closeDays: 7, // 自动关闭时间
    configId: '', // 修改业务线的id
    system: this.system,
  }
  private dialogVisible = false
  private title = '业务线新增'
  private buName = ''

  handleCancle() {
    this.dialogVisible = false
    this.setQryBu({
      refresh: false,
    })
  }

  // 创建、修改business
  handleOk() {
    if (!this.form.configId) {
      addBusiness(this.form).then((res) => {
        if (res) this.dialogVisible = false
        this.setQryBu({
          refresh: true,
        })
      })
    } else {
      modBusiness(this.form).then((res) => {
        if (res) this.dialogVisible = false
        this.setQryBu({
          refresh: true,
        })
      })
    }
  }
}
export default BusinessDialog
</script>
