<template>
  <el-upload class="avatar-uploader"
             :on-success="onSuccess"
             :show-file-list="uploadFile.showFileList"
             :action="uploadFile.host + 'ticCenter/business/api.v0.platform/fileUpload/uploadOss'"
             accept="image/*"
             :before-upload='beforeUploda'>
    <div class="upload-img-box">
      <img v-if="uploadFile.imgPath"
           :src="uploadFile.imgPath"
           class="avatar" />
      <i v-else
         class="el-icon-plus avatar-uploader-icon"></i>
    </div>
  </el-upload>
</template>

<script lang="ts">
import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator'
import { apiHost } from './../assets/javascript/util'

@Component({
  name: 'FileUpload',
})
class FileUpload extends Vue {
  @Prop() private imgPath
  @Emit('childSend') senImgPath(path: string) {
    console.log(path)
  }
  @Watch('imgPath', { immediate: true, deep: true })
  setChildParams(newVal) {
    debugger
    this.uploadFile.imgPath = newVal.imgPath
  }

  private uploadFile = {
    showFileList: false,
    host: apiHost,
    imgPath: this.imgPath.imgPath || '',
  }

  // 图片上传成功
  onSuccess(res) {
    if (res.resultCode === '0') {
      this.uploadFile.imgPath = res.data.fileName
      this.senImgPath(res.data.fileName)
      // const imgObj = new Image()
      // imgObj.src = this.uploadFile.imgPath
      // imgObj.onload = () => {
      //   if (imgObj.width / imgObj.height > 3) {
      //     this['$message'].error('图片的宽高比不能超过3:1')
      //     this.uploadFile.imgPath = ''
      //   }
      // }
    } else {
      this['$message'].error(res.resultMsg)
    }
  }

  // 图片上传前
  beforeUploda(file) {
    const size = file.size / 1024 / 1024
    if (size > 3) {
      this['$message'].error('上传的图片不能大于3MB')
      return false
    }
    console.log(file, size)
  }

  mounted() {
    // console.log('来自父组件的值', this.imgPath)
  }
}
export default FileUpload
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 200px;
  line-height: 200px;
  text-align: center;
}
.avatar {
  /* width: 100%; */
  height: 200px;
  display: block;
}
.upload-img-box {
  display: flex;
  width: 462px;
  height: 442px;
  justify-content: center;
  align-items: center;
}
.upload-img-box img {
  max-width: 462px;
  max-height: 442px;
}
</style>