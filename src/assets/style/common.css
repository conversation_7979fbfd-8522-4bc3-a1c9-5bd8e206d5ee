* {
  padding: 0;
  margin: 0;
}
html,
body,
#app {
  height: 100%;
  min-height: 100%;
}
.question_eidt {
  border: 1px dotted #ccc;
  padding: 20px 10px 0 10px;
  margin-bottom: 10px;
}
.question_eidt .item-title {
  background: #eee;
}
.question_eidt .must {
  color: #f00;
}
.question_eidt .item-parent,
.question_eidt .item-children,
.question_eidt .item-children-list,
.question_eidt .item-title {
  display: flex;
}
.question_eidt .item-parent div,
.question_eidt .item-children div,
.question_eidt .item-children-list div,
.question_eidt .item-title div {
  justify-content: center;
}
.question_eidt .item-parent .item-optionInfo,
.question_eidt .item-children .item-optionInfo,
.question_eidt .item-children-list .item-optionInfo,
.question_eidt .item-title .item-optionInfo {
  flex: 1;
}
.question_eidt .item-parent .item-isDefualt,
.question_eidt .item-children .item-isDefualt,
.question_eidt .item-children-list .item-isDefualt,
.question_eidt .item-title .item-isDefualt {
  width: 80px;
}
.question_eidt .item-parent .item-relate,
.question_eidt .item-children .item-relate,
.question_eidt .item-children-list .item-relate,
.question_eidt .item-title .item-relate {
  width: 100px;
}
.question_eidt .item-parent .item-group,
.question_eidt .item-children .item-group,
.question_eidt .item-children-list .item-group,
.question_eidt .item-title .item-group {
  width: 90px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.question_eidt .item-parent .item-group img,
.question_eidt .item-children .item-group img,
.question_eidt .item-children-list .item-group img,
.question_eidt .item-title .item-group img {
  width: 40px;
  height: 40px;
  cursor: pointer;
}
.question_eidt .item-parent .item-imgPath,
.question_eidt .item-children .item-imgPath,
.question_eidt .item-children-list .item-imgPath,
.question_eidt .item-title .item-imgPath,
.question_eidt .item-parent .item-memo,
.question_eidt .item-children .item-memo,
.question_eidt .item-children-list .item-memo,
.question_eidt .item-title .item-memo {
  width: 50px;
  cursor: pointer;
}
.question_eidt .item-parent .item-imgPath img,
.question_eidt .item-children .item-imgPath img,
.question_eidt .item-children-list .item-imgPath img,
.question_eidt .item-title .item-imgPath img,
.question_eidt .item-parent .item-memo img,
.question_eidt .item-children .item-memo img,
.question_eidt .item-children-list .item-memo img,
.question_eidt .item-title .item-memo img {
  width: 40px;
  height: 40px;
}
.question_eidt .item-parent .item-isFill,
.question_eidt .item-children .item-isFill,
.question_eidt .item-children-list .item-isFill,
.question_eidt .item-title .item-isFill {
  width: 130px;
}
.question_eidt .item-parent .item-recommend,
.question_eidt .item-children .item-recommend,
.question_eidt .item-children-list .item-recommend,
.question_eidt .item-title .item-recommend {
  width: 120px;
}
.question_eidt .item-parent .item-action,
.question_eidt .item-children .item-action,
.question_eidt .item-children-list .item-action,
.question_eidt .item-title .item-action {
  width: 135px;
}
.question_eidt .item-list,
.question_eidt .item-children-list {
  margin-top: 10px;
}
.question_eidt .item-list,
.question_eidt .item-children {
  display: flex;
  flex-direction: column;
}
.question_eidt .item-children-list {
  padding-left: 20px;
}
.question_eidt .item-title {
  font-size: 14px;
  font-weight: bold;
}
.question_eidt .item-children-list > div,
.question_eidt .item-parent > div,
.question_eidt .item-title > div {
  display: flex;
  align-items: center;
  margin: 0 5px;
}
.step {
  padding: 50px 0 0 0;
}
.step h2 {
  text-align: center;
}
.step p {
  text-align: right;
  color: #ddd;
  padding: 15px;
}
.must {
  color: #f00;
}
.pagination {
  text-align: right;
  margin-top: 10px;
}
body .el-table th.gutter {
  display: table-cell !important;
}
