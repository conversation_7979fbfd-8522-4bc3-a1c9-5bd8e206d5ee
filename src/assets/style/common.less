* {
  padding: 0;
  margin: 0;
}

html,
body,
#app {
  height: 100%;
  min-height: 100%;
}

//美化滚动条
// ::-webkit-scrollbar {
//   width: 10px;
//   height: 10px;
//   background: #515a6e;
//   border-radius: 10px;
// }

// ::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 6px #515a6e;
//   -ms-box-shadow: inset 0 0 6px #515a6e;
//   border-radius: 10px;
//   background: #F5F5F5;
// }

// ::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   -webkit-box-shadow: inset 0 0 6px #515a6e;
//   -ms-box-shadow: inset 0 0 6px #515a6e;
//   background: #515a6e;
// }

.question_eidt {
  border: 1px dotted #ccc;
  padding: 20px 10px 0 10px;
  margin-bottom: 10px;

  .item-title {
    background: #eee;
  }

  .must {
    color: #f00;
  }

  .item-parent,
  .item-children,
  .item-children-list,
  .item-title {
    display: flex;

    div {
      justify-content: center;
    }

    .item-optionInfo {
      flex: 1;
    }

    .item-isDefualt {
      width: 80px;
    }

    .item-relate {
      width: 100px;
    }

    .item-group {
      width: 90px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        width: 40px;
        height: 40px;
        cursor: pointer;
      }
    }

    .item-imgPath,
    .item-memo {
      width: 50px;
      cursor: pointer;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .item-isFill {
      width: 130px;
    }

    .item-recommend {
      width: 120px;
    }

    .item-action {
      width: 135px;
    }
  }

  .item-list,
  .item-children-list {
    margin-top: 10px;
  }

  .item-list,
  .item-children {
    display: flex;
    flex-direction: column;
  }

  .item-children-list {
    padding-left: 20px;
  }

  .item-title {
    font-size: 14px;
    font-weight: bold;
  }

  .item-children-list,
  .item-parent,
  .item-title {
    &>div {
      display: flex;
      align-items: center;
      margin: 0 5px;
    }
  }
}

.step {
  padding: 50px 0 0 0;

  h2 {
    text-align: center;
  }

  p {
    text-align: right;
    color: #ddd;
    padding: 15px;
  }
}

.must {
  color: #f00;
}

.pagination {
  text-align: right;
  margin-top: 10px;
}

body .el-table th.gutter {
  display: table-cell !important;
}
