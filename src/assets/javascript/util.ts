import { ajaxNode } from "./index";
/**
 * localStorage 存入和获取
 * set存入数据
 * ex: 存入字符串 local.set('name': 'epan') / 存入对象local.set('sex': { a: boy, b: gril })
 * get获取数据
 * ex: local.get('name')
 * clear清除全部数据
 * ex: local.clear()
 * remove删除数据
 * ex: local.remove('name')
 **/
const local = {
  set(key: string, val?) {
    typeof val === 'string' ? localStorage.setItem(key, val) : localStorage.setItem(key, JSON.stringify(val))
  },
  get(key: string) {
    const val = localStorage.getItem(key)
    if (val) {
      const flag = val.startsWith('{') || val.startsWith('[');
      if (!flag) {
        return val
      } else {
        return JSON.parse(val)
      }
    }
  },
  clear() {
    localStorage.clear()
  },
  remove(key: string) {
    localStorage.removeItem(key)
  }
}

// 重定向URL
const redirect = () => {
  window.location.href = `${window.location.origin}/login`
}

// 创建分类树结构数据
// const createTreeData = (data?z) => {
//   const cloneData = JSON.parse(JSON.stringify(data)); // 对源数据深度克隆
//   const tree = cloneData.filter((father?) => {
//     //循环所有项
//     const branchArr = cloneData.filter((child?) => {
//       return father.catagoryId == child.parentId; //返回每一项的子级数组
//     });
//     if (branchArr.length > 0) {
//       father.children = branchArr; //如果存在子级，则给父级添加一个children属性，并赋值
//     }
//     return father.parentId === 0; //返回第一层
//   });
//   return tree;
// }
const createTreeData = (data?, childrenId?, parentId?) => {
  const cloneData = JSON.parse(JSON.stringify(data)); // 对源数据深度克隆
  const tree = cloneData.filter((father?) => {
    //循环所有项
    const branchArr = cloneData.filter((child?) => {
      return father[childrenId] == child[parentId]; //返回每一项的子级数组
    });
    if (branchArr.length > 0) {
      father.children = branchArr; //如果存在子级，则给父级添加一个children属性，并赋值
    }
    return father[parentId] == 0; //返回第一层
  });
  return tree;
}

const buttonGroup = [
  {
    name: "填空题",
    type: 1,
    status: true
  },
  {
    name: "单选题（平铺）",
    type: 2,
    status: true
  },
  {
    name: "多选题（平铺）",
    type: 3,
    status: true
  },
  {
    name: "单选题（下拉）",
    type: 4,
    status: true
  },
  {
    name: "多选题（下拉）",
    type: 5,
    status: true
  },
  {
    name: "上传文件",
    type: 6,
    status: true
  },
  {
    name: "上传+填空",
    type: 7,
    status: true
  },
  {
    name: "下拉+填空",
    type: 8,
    status: true
  },
  {
    name: "图片上传",
    type: 9,
    status: true
  }
]
const buttonGroupStep0 = [
    {
        name: "图片识别",
        type: 10,
        status: true,
      },
      {
        name: "AI组件",
        type: 51,
        status: true,
      }
]

let previewHost = '';
let apiHost = '';
let version = '';
if (process.env.NODE_ENV === "development") {
  // version = 'gray'
  previewHost = "https://memberuat.sgsonline.com.cn";
  apiHost = 'https://gatetest.sgsonline.com.cn/'
  // previewHost = "https://memberdev.sgsonline.com.cn";
  // apiHost = 'https://gatedev.sgsonline.com.cn/'
} else if (process.env.NODE_ENV === "uat") {
  previewHost = "https://memberuat.sgsonline.com.cn";
  apiHost = 'https://gateuat.sgsonline.com.cn/'
} else if (process.env.NODE_ENV === "gray") {
  previewHost = "https://memberuat.sgsonline.com.cn";
  apiHost = 'https://gateuat.sgsonline.com.cn/'
  version = 'gray'
} else if (process.env.NODE_ENV === "test") {
  previewHost = "https://membertest.sgsonline.com.cn";
  apiHost = 'https://gatetest.sgsonline.com.cn/'
} else if (process.env.NODE_ENV === "product" || process.env.NODE_ENV === "azureprod") {
  previewHost = "https://member.sgsonline.com.cn";
  apiHost = 'https://gate.sgsonline.com.cn/'
} else if (process.env.NODE_ENV === "prodGray") {
  previewHost = "https://member.sgsonline.com.cn";
  apiHost = 'https://gate.sgsonline.com.cn/'
  version = 'gray'
} else {
  // version = 'gray'
  // previewHost = "http://**************:9011";
  // apiHost = 'https://gateuat.sgsonline.com.cn/'
  previewHost = "https://memberuat.sgsonline.com.cn";
  apiHost = 'https://gatetest.sgsonline.com.cn/'
}

// 当前系统
const CurrentSystem = 'ticCenter'

// 当前日期
const currDate = () => {
  const date = new Date();
  const year = date.getFullYear(),
    month = date.getMonth() + 1,
    day = date.getDate();
  return `${year}_${month}_${day}`
}

const user_agent = navigator.userAgent.toLowerCase();
// js错误监控
window.onerror = (err_message, err_url, err_row, err_column, err_obj) => {
  // ajaxNode('/jsError/create', { err_message, err_url, err_row, err_column, err_obj, system: 2, user_agent })
}
// 接口错误监控
const apiErrorWatch = (err_message, param, api_url) => {
  // ajaxNode('/apiError/create', { err_message, param, href: decodeURIComponent(window.location.href), system: 2, api_url, user_agent })
}
// 静态资源错误监控
window.addEventListener('error', (error) => {
  // 非js错误
  if (!error.message || !error.cancelable) {
    let errorObj: any = (error as any).target
    const {
      href,
      baseURI,
      outerHTML,
      currentSrc,
      tagName
    } = errorObj

    const domNode = [];
    errorObj.path.forEach(v => {
      if (v.tagName) {
        domNode.unshift(v.tagName)
      }
    })
    // ajaxNode('/staticError/create', { path: domNode.join(' > '), href: href ? href : currentSrc, baseURI: decodeURIComponent(baseURI), outerHTML, tagName, system: 2 })
  }
}, true)

/**
 * 生成随机字符串
 * @param length 要生成的字符串长度
 * @returns 生成的随机字符串
 */
const generateRandomString = (length: number): string => {
    const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters.charAt(randomIndex);
    }
    
    return result;
  }

export {
  local,
  redirect,
  createTreeData,
  buttonGroup,
  buttonGroupStep0,
  previewHost,
  apiHost,
  CurrentSystem,
  currDate,
  version,
  apiErrorWatch,
  generateRandomString
}
