// export module Unit {
//     export class Local {
//         set(key: string, val?: any) {
//             typeof val === 'string' ? localStorage.setItem(key, val) : localStorage.setItem(key, JSON.stringify(val))
//         }

//         get(key: string) {
//             const val = localStorage.getItem(key)
//             if (val) {
//                 const flag = val.startsWith('{') || val.startsWith('[');
//                 if (!flag) {
//                     return val
//                 } else {
//                     return JSON.parse(val)
//                 }
//             }
//         }

//         clear() {
//             localStorage.clear()
//         }

//         remove(key: string) {
//             localStorage.removeItem(key)
//         }
//     }

//     // export class Redirect {

//     // }

//     // export class CreateTreeData {

//     // }
// }

export module Test {
    
}