<template>
  <div class="login">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item>
        <p class="login_welecome">欢迎登录QMS系统</p>
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model="ruleForm.username"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          type="password"
          v-model="ruleForm.password"
          @change="submitForm('ruleForm')"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">登 录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang='ts'>
import { Component, Vue } from "vue-property-decorator";
import { ajaxNew } from "./../api/index";
import { local, CurrentSystem } from "./../assets/javascript/util";

@Component({
  name: "Login",
})
class Login extends Vue {
  constructor(parameters) {
    super(parameters);
  }
  private ruleForm = {
    username: "apac\\",
    password: "",
    system: CurrentSystem,
  };
  private rules = {
    username: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
    password: [{ required: true, message: "密码不能为空", trigger: "blur" }],
  };

  submitForm(formName: string): void {
    const ref: any = this.$refs[formName];
    ref.validate((valid) => {
      if (valid) {
        ajaxNew("/business/api.v1.sso/sysperson/login", this.ruleForm, "post", {
          type: "ticSso",
        })
          .then((res) => {
            if (res.resultCode === "0") {
              // 获取、存储 token
              local.set("token", res.data);
              this["$router"].push("/home");
            } else {
              this["$message"]({
                message: res.resultMsg,
                type: "error",
              });
            }
          })
          .catch((error) => {
            console.log(error);
          });
      } else {
        return false;
      }
    });
  }
}
export default Login;
</script>

<style lang="less" scoped>
.login {
  width: 100%;
  height: 100vh;
  background: #eee;
  display: flex;
  justify-content: center;
  align-items: center;

  .el-form {
    width: 400px;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 3px 3px 3px #ddd;
  }
  .login_welecome {
    font-size: 16px;
    padding-left: 20px;
  }
}
</style>