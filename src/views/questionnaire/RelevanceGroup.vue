<template>
  <div class="relevance_group">
    <el-row v-loading='isLoading'>
      <el-col :span='24'>
        <h2>
          {{qsRelevanceParam.source === 'group' ? "十二宫格标题" : "首页标题"}}：{{partName}}
        </h2>
      </el-col>
      <el-col :span='24'>
        <el-button type="primary"
                   @click="handleCreate">创建项目关联规则</el-button>
        <el-button @click="handleBack">返回问卷</el-button>
        <el-input placeholder="请输入规则名称..."
                  style='width: 300px; margin: 0 10px;;'
                  v-model="groupName"
                  clearable></el-input>
        <el-button @click="handleSearch"
                   type="primary">搜索</el-button>
      </el-col>
      <el-col :span='24'
              style="margin-top: 20px;">
        <el-table :data="list"
                  border
                  style="width: 100%">
          <el-table-column type="index"
                           align="center"
                           label="序号"
                           width="50px">
          </el-table-column>
          <el-table-column prop="groupName"
                           align="center"
                           label="规则名称">
          </el-table-column>
          <el-table-column prop="address"
                           label="规则条件">
            <template slot-scope="scope">
              <div>
                <div class='relevance_group_info'
                     v-for='(item, index) of scope.row.subjects'
                     :key='index'>
                  {{index +　1}}、{{ item.questionTitle }}{{ item.position === 'first' ? '[首页]'　: '' }} {{ item.isHide === 1 ? '（隐藏题）'　: '' }}:
                  选择<span>{{item.optionInfo}}</span>{{item.unionType ? '全部选项' : '其中一个'}}
                </div>
                <div v-if='scope.row.subjects.length > 1'>{{ scope.row.unionType ? '以上同时满足' : '以上满足一个'}}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="address"
                           align="center"
                           label="操作"
                           width="280px">
            <template slot-scope="scope">
              <el-button @click="handleModify(scope.row)"
                         type="text"
                         size="small">修改规则</el-button>
              <el-button v-if='qsRelevanceParam.source === "group"'
                         @click="handleCopy(scope.row)"
                         type="text"
                         size="small">复制规则</el-button>
              <el-button @click="handleConfig(scope.row)"
                         type="text"
                         size="small">配置项目{{scope.row.itemNums ? `(${scope.row.itemNums})` : ''}}</el-button>
              <el-button @click="handleDelete(scope.row)"
                         type="text"
                         size="small">删除规则</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination style="text-align: right; margin-top: 20px;"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="page.pageNum"
                       :page-size="page.pageRow"
                       :page-sizes="[10, 50, 100]"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="page.total">
        </el-pagination>
      </el-col>
    </el-row>
    <el-dialog title="创建/编辑项目关联的规则"
               :visible.sync="dialogVisible"
               width="30%"
               :before-close="handleCloseDialog">
      <qsRelevance :qsRelevanceParam="qsRelevanceParam"
                   @childSendQs="childSendQsfun" />
      <span slot="footer"
            class="dialog-footer">
        <div></div>
      </span>
    </el-dialog>
  </div>
</template>

<script lang='ts'>
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'
import { copyRelevanceGroup } from './../../api/common'
import QsRelevance from './../../components/question/QsRelevance.vue'

@Component({
  name: 'RelevanceGroup',
  components: {
    QsRelevance,
  },
})
class RelevanceGroup extends Vue {
  private partName = ''
  private partId = ''
  private list = []
  private dialogVisible = false
  private qsRelevanceParam = {
    partNo: '',
    source: 'group', //默认为12宫格
    groupRelevance: {},
    visible: false,
  }
  private page = {
    total: 0,
    pageRow: 10,
    pageNum: 1,
  }
  private isLoading = false
  private groupName = ''

  // 创建项目关联弹窗
  handleCreate() {
    this.dialogVisible = true
    this.qsRelevanceParam.visible = true
    this.qsRelevanceParam.groupRelevance = {}
  }
  // 返回问卷
  handleBack() {
    if (this['$route'].query.subjectNo && this['$route'].query.optionNo) {
      const partName = encodeURIComponent(
        this['$route'].query.partName as string
      )
      this['$router'].push(
        `cardRelevance?id=${this['$route'].query.id}&partName=${partName}&subjectNo=${this['$route'].query.subjectNo}&optionNo=${this['$route'].query.optionNo}&partNo=${this['$route'].query.partNo1}`
      )
    } else {
      this['$router'].push(`step2?id=${this['$route'].query.id}`)
    }
  }
  // 修改项目关联弹窗
  handleModify(row: any) {
    this.qsRelevanceParam.partNo = row.partNo
    this.qsRelevanceParam.groupRelevance = row
    this.dialogVisible = true
    this.qsRelevanceParam.visible = true
  }
  // 复制高级规则
  handleCopy(row: any) {
    console.log(row)
    copyRelevanceGroup({
      groupNo: row.groupNo,
    }).then((res) => {
      if (res) {
        this.qryList()
        this['$message'].success('复制成功')
      }
    })
  }
  // 配置关联项目
  handleConfig(row: any) {
    window.open(
      `/testItem?questionId=${row.questionId}&partNo=${row.partNo}&groupNo=${row.groupNo}`
    )
  }
  // 获取规则列表
  qryList() {
    this.isLoading = true
    const params = Object.assign(this.page, {
      partNo: this.qsRelevanceParam.partNo,
      groupName: this.groupName,
    })
    ajaxNew('/business/api.v1.question/qitem/qryRelateGroup', params, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.list = res.data.items
          this.page.total = res.data.totalNum
        } else {
          this['$message'].error(res.resultMsg)
        }
        this.isLoading = false
      })
      .catch((e) => {
        console.log(e)
        this.isLoading = false
      })
  }
  // 删除规则
  handleDelete(row: any) {
    this['$confirm'](`您确认删除？`)
      .then(() => {
        this.isLoading = true
        ajaxNew(
          '/business/api.v1.question/qitem/unrelateGroup',
          {
            groupNo: row.groupNo,
            questionId: row.questionId,
          },
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this['$message']({
                message: '删除成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.qryList()
                },
              })
            } else {
              this['$message'].error(res.resultMsg)
              this.isLoading = false
            }
          })
          .catch((e) => {
            this['$message'].error(e)
            this.isLoading = false
          })
      })
      .catch((e) => {
        console.log(e)
      })
  }
  handleCloseDialog(): void {
    this.dialogVisible = false
    this.qsRelevanceParam.visible = false
  }
  handleSizeChange(val: number) {
    this.page.pageRow = val
    this.qryList()
  }
  handleCurrentChange(val: number) {
    this.page.pageNum = val
    this.qryList()
  }
  // 组件内新增或删除后刷新列表
  childSendQsfun(res): void {
    if (res.refresh) {
      this.dialogVisible = false
      this.qsRelevanceParam.visible = false
      this.qsRelevanceParam.groupRelevance = {}
      this.qryList()
    }
  }
  // 根据关联名称查询
  handleSearch() {
    this.qryList()
  }

  mounted(): void {
    // 当没有subjectNo，optionNo为首页的关联组
    if (!this['$route'].query.subjectNo && !this['$route'].query.optionNo) {
      this.qsRelevanceParam.source = 'groupByHome'
    }
    this.partName = this['$route'].query.partName as string
    this.partId = this['$route'].query.id as string
    this.qsRelevanceParam.partNo = this['$route'].query.partNo as string
    // this.qsRelevanceParam.partNo = this['$route'].query.partNo as string
    this.qryList()
  }
}
export default RelevanceGroup
</script>

<style lang='less'>
.relevance_group {
  padding: 40px;

  h2 {
    text-align: center;
    padding-bottom: 20px;
  }
  .relevance_group_info {
    span {
      color: #000;
      padding: 0 3px;
      text-decoration: underline;
    }
  }
}
</style>