<template>
  <el-row class="step">
    <el-col :span="24">
      <h2>Step4：补充信息</h2>
    </el-col>
    <el-col :span="24"
            v-loading='loading'>
      <el-container>
        <el-aside width="170px"
                  class="aside">
          <el-button @click="handleCreateQs(item.type)"
                     v-for="item of buttonGroup"
                     :key="item.type"
                     :disabled="!item.status">{{item.name}}</el-button>
        </el-aside>
        <el-main>
          <el-col :span="24">
            <el-form ref="form"
                     :model="formModel"
                     label-width="100px">
              <el-form-item label="本页标题">
                <el-input maxlength="50"
                          v-model="partModifyForm.partName"
                          placeholder="请输入本页标题..."
                          style="width: 300px; margin-right: 15px;"></el-input>
                <el-button @click="modPart('title')">保存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <div v-for="(qsItem, index) of qsList"
                 :key="qsItem.subjectId"
                 class="qs_list">
              <template v-if="qsItem.questionType === 1">
                <Input :childParams="qsItem" />
              </template>
              <template v-if="qsItem.questionType === 2 || qsItem.questionType === 3 || qsItem.questionType === 4 || qsItem.questionType === 5">
                <Checkbox :childParams="qsItem" />
              </template>
              <template v-if="qsItem.questionType === 6">
                <Upload :childParams="qsItem" />
              </template>
              <template v-if="qsItem.questionType === 7">
                <InputUpload :childParams="qsItem" />
              </template>
              <template v-if="qsItem.questionType === 8">
                <InputSelect :childParams="qsItem" />
              </template>
              <template v-if="qsItem.questionType === 9">
                <UploadBatch :childParams="qsItem" />
              </template>
              <div style="text-align: right;">
                <el-dropdown type="primary"
                             @command="handleCommand">
                  <el-button>
                    插入新题
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="beforeHandleCommand(index, item.type)"
                                      :disabled="!item.status"
                                      v-for="item of buttonGroup"
                                      :key="item.type">{{ item.name }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button-group>
                  <el-button icon="el-icon-copy-document"
                             @click="handleCopyQuestion(qsItem)"
                             :disabled="qsItem.isEdit">复制</el-button>
                  <el-button icon="el-icon-edit"
                             @click="handleEditQuestion(qsItem)">编辑</el-button>
                  <el-button icon="el-icon-delete"
                             @click="handleDelQuestion(qsItem)">删除</el-button>
                  <el-button icon="el-icon-top"
                             :disabled="!index"
                             @click="handleMove('up', index)">上移</el-button>
                  <el-button icon="el-icon-bottom"
                             :disabled="index == qsList.length - 1"
                             @click="handleMove('down', index)">下移</el-button>
                  <el-button icon="el-icon-upload2"
                             :disabled="!index"
                             @click="handleMove('top', index)">最前</el-button>
                  <el-button icon="el-icon-download"
                             :disabled="index == qsList.length - 1"
                             @click="handleMove('bottom', index)">最后</el-button>
                </el-button-group>
                <p style="font-size: 12px; color: #999;">问题/选项关联仅可关联本题之前得问题，改变问题位置顺序可能导致关联关系丢失，建议确认/调整顺序后，再进行关联关系配置。</p>
              </div>
            </div>
          </el-col>
          <el-col :span="24"
                  style="text-align: center; margin-top: 20px;">
            <el-button type="primary"
                       @click="modPart('all')">完成</el-button>
            <el-button @click="handlePreviw">预览</el-button>
            <el-button @click="qsSetState">启用</el-button>
          </el-col>
        </el-main>
      </el-container>
    </el-col>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'
import {
  createTreeData,
  buttonGroup,
  local,
} from './../../assets/javascript/util'
import Upload from './../../components/question/Upload.vue'
import Input from './../../components/question/Input.vue'
import Checkbox from './../../components/question/Checkbox.vue'
import InputUpload from './../../components/question/InputUpload.vue'
import InputSelect from './../../components/question/InputSelect.vue'
import UploadBatch from './../../components/question/UploadBatch.vue'

@Component({
  name: 'Step2',
  components: {
    Upload,
    Input,
    Checkbox,
    InputUpload,
    InputSelect,
    UploadBatch
  },
})
class Step2 extends Vue {
  // 监听刷新数据问题列表接口
  @Watch('getQslistRefersh', { immediate: true, deep: true })
  watchQslistRefersh(newVal) {
    if (newVal) {
      this.qryQuestion(this.qsCreateForm.partNo)
    }
  }
  // 设置刷新数据问题列表接口
  @Mutation('setQsListRefresh') setQsListRefresh

  // 按住组(1:填空,2:单选,3:多选,4:下拉框单选,5:下拉框多选,6:上传)
  private buttonGroup = buttonGroup
  // 当前所选问卷类目
  private catagoryItem = []
  private props = {
    multiple: true,
    label: 'catagoryName',
    value: 'catagoryId',
    emitPath: false,
  }
  // 默认选中的类目
  private defaulValue = []
  // form占位符
  private formModel = {}
  // 传到子组件参数
  private childParams = {}
  // 新增章节参数
  private partCreateForm = {
    questionId: '',
    partName: '',
    position: 'last',
  }

  // 修改章节参数
  private partModifyForm = {
    questionId: '',
    partName: '',
    partNo: '',
    subjectItem: [],
  }

  // 新增问题参数
  private qsCreateForm = {
    questionTitle: '问题标题',
    sortShow: 0,
    partNo: '',
    questionType: 0, //问题类型 1:填空,2:单选,3:多选,4:下拉框单选,5:下拉框多选,6:上传
  }

  private categorys = []
  private loading: boolean = false

  // 预览跳转
  handlePreviw(): void {
    window.open('/preview?category=' + local.get('category'))
  }

  // 题目列表
  private qsList = []

  handleReturn(): void {
    this['$router'].push('/step1?id=' + this.partCreateForm.questionId)
  }

  // 查询问卷详情
  qryQuestionDetail(id: number | string) {
    ajaxNew(
      '/business/api.v1.question/question/qryDtl',
      {
        questionId: id,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          res.data.catagoryItem.forEach((item) => {
            this.defaulValue.push(item.catagoryId)
          })
          //  console.log('this.defaulValue', this.defaulValue)
          this.catagoryItem = createTreeData(
            this.unique(res.data.catagoryItem),
            'catagoryId',
            'parentId'
          )
          // 生成预览category
          this.createCategorys(res.data.catagoryItem)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  createCategorys(datas) {
    const parendNodeIndex = []
    datas.forEach((item, index) => {
      if (!item.parentId) parendNodeIndex.push(index)
    })
    if (parendNodeIndex.length > 1) {
      datas.forEach((item, index) => {
        if (index < parendNodeIndex[1]) this.categorys.push(item.catagoryId)
      })
    } else {
      datas.forEach((item) => {
        this.categorys.push(item.catagoryId)
      })
    }
    local.set('category', this.categorys.join(','))
  }

  // 数组去重复
  unique(arrary) {
    const res = new Map()
    return arrary.filter(
      (a) => !res.has(a.catagoryId) && res.set(a.catagoryId, 1)
    )
  }

  // 新建章节
  addPart(): void {
    ajaxNew('/business/api.v1.question/qpart/add', this.partCreateForm, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.partModifyForm.partNo = this.qsCreateForm.partNo =
            res.data.partNo
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 修改章节
  modPart(type: string): void {
    if (this.qsList.length) {
      this.qsList.forEach((item) => {
        this.partModifyForm.subjectItem.push({
          subjectId: item.subjectId,
          subjectNo: item.subjectNo,
          sortShow: item.sortShow,
        })
      })
    }
    const loading = this['$loading']({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    ajaxNew('/business/api.v1.question/qpart/mod', this.partModifyForm, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.partModifyForm.partNo = this.qsCreateForm.partNo =
            res.data.partNo
          this['$message']({
            message: '保存成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              if (type === 'all') {
                this['$router'].push('questionnaire/list')
              } else if (type === 'prev') {
                this['$router'].push(
                  '/step1?id=' + this.partCreateForm.questionId
                )
              }
            },
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  // 根据问卷ID获取章节，如没有则新建章节
  qryByQuestion(questionId: number | string): void {
    ajaxNew(
      '/business/api.v1.question/qpart/qryByQuestion',
      {
        questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const partItem = res.data.items.filter((item) => {
            return item.position === 'last'
          })
          if (!partItem.length) {
            this.addPart()
          } else {
            // 有章节则使用
            this.partModifyForm.partName = partItem[0].partName
            this.partModifyForm.partNo = this.qsCreateForm.partNo =
              partItem[0].partNo
            this.qryQuestion(partItem[0].partNo)
          }
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 新建题目
  handleCreateQs(type: number): void {
    this.qsCreateForm.questionType = type
    this.qsSbjectAdd(type, this.qsList.length + 1, this.qsList.length + 1)
  }
  qsSbjectAdd(questionType: number, index?, sortShow?: number): void {
    this.qsCreateForm.questionType = questionType
    // 在当前索引下添加
    this.qsCreateForm.sortShow = sortShow + 1
    ajaxNew('/business/api.v1.question/qsubject/add', this.qsCreateForm, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          const obj = {
            questionType: questionType,
            partNo: res.data.partNo,
            subjectNo: res.data.subjectNo,
            subjectId: res.data.subjectId,
            isEdit: true,
          }
          if (index === '') {
            this.qsList.push(obj)
          } else {
            this.qsList.splice(index + 1, 0, obj)
          }
          this.qsList.forEach((item, index) => {
            item.sortShow = index + 1
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 根据章节查询题目
  qryQuestion(partNo: string): void {
    ajaxNew('/business/api.v1.question/qsubject/qryAll', { partNo }, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.setQsListRefresh(false)
          const noOverItem = this.qsList.filter((item) => {
            return item.isEdit === true
          })
          res.data.items.forEach((item, index) => {
            item.isEdit = false
            item.source = 'cardRelevance'
            item.sortShow = index + 1
          })
          if (noOverItem.length) {
            res.data.items.forEach((item) => {
              noOverItem.forEach((i) => {
                if (item.subjectNo === i.subjectNo) {
                  item.isEdit = true
                }
              })
            })
          }
          this.qsList = res.data.items
          this.modPart('title')
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 删除题目
  handleDelQuestion(qsItem): void {
    this.loading = true
    const param = {
      partNo: qsItem.partNo,
      subjectId: qsItem.subjectId,
      subjectNo: qsItem.subjectNo,
    }
    // this["$confirm"](`您确认删除问卷《${qsItem.questionTitle}》？`)
    this['$confirm'](`您确认删除题目？`)
      .then(() => {
        ajaxNew('/business/api.v1.question/qsubject/del', param, 'post')
          .then((res) => {
            if (res.resultCode === '0') {
              this.qryQuestion(this.partModifyForm.partNo)
            } else {
              this['$message'].error(res.resultMsg)
            }
            this.loading = false
          })
          .catch((e) => {
            this['$message'].error(e)
            this.loading = false
          })
      })
      .catch((e) => {
        console.log(e)
        this.loading = false
      })
  }

  // 编辑题目
  handleEditQuestion(qsItem): void {
    console.log(qsItem)
    qsItem.isEdit = !qsItem.isEdit
  }

  // 复制题目
  handleCopyQuestion(qsItem): void {
    this.loading = true
    const param = {
      partNo: qsItem.partNo,
      subjectNo: qsItem.subjectNo,
      subjectId: qsItem.subjectId,
      questionTitle: `${qsItem.questionTitle}（复制）`,
      sortShow: qsItem.sortShow + 1,
    }
    ajaxNew('/business/api.v1.question/qsubject/copy', param, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.qryQuestion(this.partModifyForm.partNo)
        } else {
          this['$message'].error(res.resultMsg)
        }
        this.loading = false
      })
      .catch((e) => {
        this['$message'].error(e)
        this.loading = false
      })
  }

  // 移动题目位置
  handleMove(arrow: string, index: number): void {
    if (arrow === 'up') {
      this.qsList[index] = this.qsList.splice(
        index - 1,
        1,
        this.qsList[index]
      )[0]
    } else if (arrow === 'down') {
      this.qsList[index] = this.qsList.splice(
        index + 1,
        1,
        this.qsList[index]
      )[0]
    } else if (arrow === 'top') {
      this.qsList.unshift(this.qsList.splice(index, 1)[0])
    } else if (arrow === 'bottom') {
      this.qsList.push(this.qsList.splice(index, 1)[0])
    }
    this.qsList.forEach((item, index) => {
      item.sortShow = index + 1
    })
  }

  // 中间插入题目
  handleCommand(command) {
    let sortNum = 0
    this.qsList.forEach((item, iIndex) => {
      if (command.index === iIndex) {
        sortNum = item.sortShow
      }
    })
    this.qsSbjectAdd(command.type, command.index, sortNum)
  }
  beforeHandleCommand(index, type) {
    return {
      index,
      type,
    }
  }

  // 计算属性（computed）
  get getQslistRefersh(): boolean {
    return this.$store.state.qsListRefresh
  }

  // 启用问卷 tood
  qsSetState() {
    ajaxNew(
      '/business/api.v1.question/question/modState',
      {
        questionId: this['$route'].query.id,
        state: 1,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          console.log('')
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  mounted(): void {
    this.partCreateForm.questionId = this.partModifyForm.questionId = this[
      '$route'
    ].query.id as string

    if (this['$route'].query.id) {
      this.qryByQuestion(this['$route'].query.id as string)
      this.qryQuestionDetail(this['$route'].query.id as string)
    }

    console.log('getQslistRefersh', this.getQslistRefersh)
  }
}
export default Step2
</script>

<style lang="less" scoped>
.category_box {
  height: auto;
  overflow: hidden;
  div {
    height: 25px;
    line-height: 25px;
  }
}
.el-container {
  border-top: 1px solid #ccc;
  margin-top: 15px;
  min-height: 100vh;

  .el-aside {
    border-right: 1px solid #ccc;
    text-align: center;

    .el-button {
      margin-top: 20px;
    }
  }
}
.qs_list {
  border: 1px solid #ddd;
  box-shadow: 5px 5px 3px #ddd;
  border-radius: 5px;
  margin-top: 20px;
  padding: 20px;
}
</style>