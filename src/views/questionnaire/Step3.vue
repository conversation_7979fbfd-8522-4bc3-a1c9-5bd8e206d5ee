<template>
  <el-row class="step"
          v-loading='loading'>
    <el-col :span="24">
      <h2>Step3：配置推荐项目</h2>
    </el-col>
    <el-col :span="24">
      <el-form ref="form"
               :model="formModel"
               label-width="100px">
        <el-form-item label="本页标题">
          <el-input maxlength="50"
                    v-model="partModifyForm.partName"
                    placeholder="请输入本页标题..."
                    style="width: 300px; margin-right: 15px"></el-input>
          <el-button @click="modPart('title')">保存</el-button>
        </el-form-item>
        <el-form-item label="配置服务项目">
          <div class="card_wrap">
            <el-card class="card_box"
                     v-for="(item, index) of qsList"
                     :key="index">
              <div class="card_title">
                {{ item.sortShow }}、{{ item.questionTitle }}
              </div>
              <div class="card_button">
                <el-button-group>
                  <el-button type="primary"
                             size="small"
                             @click="handleCardEdit(item, index + 1)">编辑项目</el-button>
                  <el-button type="primary"
                             size="small"
                             @click="handleCardDel(item)">删除</el-button>
                  <el-button type="primary"
                             size="small"
                             @click="handleCardCopy(item)">复制项目</el-button>
                  <el-button type="primary"
                             size="small"
                             @click="handleRelevanceButton(item)">关联逻辑</el-button>
                </el-button-group>
              </div>
            </el-card>
            <el-card class="card_box card_box_add">
              <el-button @click="handleCreateQs(false)"
                         type="text"
                         icon="el-icon-plus">新建服务项目</el-button>
              <br />
              <el-button @click="handleCopyOther()"
                         type="text"
                         icon="el-icon-document-copy">从其他问卷复制</el-button>
            </el-card>
          </div>
        </el-form-item>
        <el-form-item label="文件上传">
          <el-form ref="form"
                   :model="lastUplaod"
                   label-width="100px">
            <el-form-item label="问题标题">
              <el-input placeholder="请输入问题标题..."
                        style="width: 300px; margin: 0 15px 10px 0"
                        v-model="lastUplaod.questionTitle"
                        maxlength="40" />
            </el-form-item>
            <el-form-item label="问题备注">
              <el-input placeholder="请输入问题备注..."
                        type="textarea"
                        v-model="lastUplaod.memo"
                        maxlength="250" />
            </el-form-item>
            <el-form-item label="可上传类型">
              <el-radio-group v-model="lastUplaod.uploadType">
                <el-radio :label="1">全选</el-radio>
                <el-radio :label="2">图片和视频（支持拍照/录制上传）</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-form-item>
        <el-form-item label="填空题">
          <el-form ref="form"
                   :model="lastInput"
                   label-width="100px">
            <el-form-item label="问题标题">
              <el-input placeholder="请输入问题标题..."
                        style="width: 300px; margin: 0 15px 10px 0"
                        v-model="lastInput.questionTitle"
                        maxlength="40" />
              <el-checkbox v-model="lastInput.isMust"
                           :true-label="1"
                           :false-label="0">必填</el-checkbox>
            </el-form-item>
            <el-form-item label="答案说明">
              <el-input placeholder="请输入答案说明..."
                        type="textarea"
                        v-model="lastInput.fillMemo"
                        maxlength="250" />
            </el-form-item>
            <el-form-item label>
              <el-button style="margin-top: 10px"
                         type="primary"
                         @click="handleSaveLast('button')">保存</el-button>
            </el-form-item>
          </el-form>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="24"
            style="text-align: center; margin-top: 20px">
      <el-button @click="modPart('prev')">保存上一步</el-button>
      <el-button type="primary"
                 @click="modPart('all')">下一步，配置补充问题</el-button>
      <el-button @click="handlePreviw">预览</el-button>
      <el-button @click="qsSetState">启用</el-button>
    </el-col>
    <el-dialog title="服务项目新增/编辑"
               :visible.sync="cardDialog.visible"
               width="80%"
               @closed="dialogClose">
      <Card :childParams="qsItem"
            @childSend="childSendfun" />
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="cardDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="cardDialog.visible = false">保 存</el-button>
      </span>-->
    </el-dialog>
    <el-dialog title="题目关联"
               :visible.sync="qsRelevanceDialog"
               width="50%"
               @closed="dialogClose">
      <qsRelevance :qsRelevanceParam="qsRelevanceParam"
                   @childSendQs="childSendQsfun" />
    </el-dialog>
    <el-dialog title="其他问卷复制"
               :visible.sync="otherModal.isShow"
               width="50%"
               @close="handleCloseOther">
      <el-form ref="form"
               :model="formModel"
               label-width="100px"
               @submit.native.prevent>
        <el-form-item label="问卷名：">
          <el-input @keyup.enter.native="handleSearch"
                    v-model="paginationParam.questionName"
                    placeholder="请输入问卷名..."
                    style="width: 300px; margin-right: 15px"></el-input>
          <el-button @click="handleSearch">查询</el-button>
        </el-form-item>
        <el-form-item label="问卷列表：">
          <el-table ref="table"
                    :data="questionList"
                    style="width: 100%"
                    @expand-change="handleExpand"
                    @row-click="handleClickRow"
                    stripe>
            <el-table-column type="expand">
              <template slot-scope="props"
                        v-if="props.row.items.length">
                <el-form label-position="left"
                         inline
                         class="demo-table-expand"
                         v-for="(item, i) of props.row.items"
                         :key="i">
                  <el-form-item>
                    <span>{{ i + 1 }}、{{ item.questionTitle }}</span>
                  </el-form-item>
                  <el-form-item style="float: right">
                    <el-button type="text"
                               @click="handleCopyItem(props.row.questionId, item)">复制</el-button>
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column label="问卷名称"
                             prop="questionName"></el-table-column>
            <el-table-column label="BU/业务线"
                             prop="name">
              <template slot-scope="props">{{ props.row.bu }}/{{ props.row.itemName }}</template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-row>
</template>

<script lang='ts'>
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'
import { local } from './../../assets/javascript/util'
import Card from './../../components/Card.vue'
import QsRelevance from './../../components/question/QsRelevance.vue'
import { qryByPositionAction } from './../../api/common'

@Component({
  name: 'Step3',
  components: {
    Card,
    QsRelevance,
  },
})
class Step3 extends Vue {
  @Watch('getQslistRefersh', { immediate: true, deep: true })
  watchQslistRefersh(newVal) {
    if (newVal) {
      // console.log("newVal-----------", newVal, this.activeIndex);
      this.qryQuestion(this.qsCreateForm.partNo)
    }
  }
  // 设置刷新数据问题列表接口
  @Mutation('setQsListRefresh') setQsListRefresh

  // 新增章节参数
  private partCreateForm = {
    questionId: '',
    partName: '',
    position: 'second',
  }
  // 修改章节参数
  private partModifyForm = {
    questionId: '',
    partName: '',
    partNo: '',
    subjectItem: [],
  }
  // form占位符
  private formModel = {}
  // 12宫格卡片弹窗
  private cardDialog = {
    visible: false,
  }
  // 题目列表
  private qsList = []
  // 编辑问题的值
  private qsItem = {}
  // 新增问题参数
  private qsCreateForm = {
    sortShow: 0,
    questionTitle: '问题标题',
    partNo: '',
    questionType: 3, //问题类型 1:填空,2:单选,3:多选,4:下拉框单选,5:下拉框多选,6:上传
  }
  // 最后文件上传题目
  private lastUplaod = {
    partNo: '',
    questionId: '',
    questionType: 6,
    questionTitle: '',
    uploadType: 1,
    memo: '',
    sortShow: 0,
  }
  // 最后填空题目
  private lastInput = {
    partNo: '',
    questionId: '',
    questionType: 1,
    questionTitle: '请描述您的具体需求',
    uploadType: 1,
    fillMemo: '如测试项目和要求、测试目的、产品的应用领域等',
    sortShow: 0,
    isMust: 1,
  }

  // 题目关联弹窗
  private qsRelevanceDialog = false
  private qsRelevanceParam = {}
  // 其他问卷复制
  private otherModal = {
    isShow: false,
  }
  private questionList = []
  private paginationParam = {
    questionName: '',
    pageNum: 1,
    pageRow: 999,
  }
  // 需要展开的行
  private expands = []

  // 当前处于编辑状态的索引
  private activeIndex = 0
  private loading: boolean = false

  // 预览跳转
  handlePreviw(): void {
    window.open('/preview?category=' + local.get('category'))
  }

  // 关闭
  dialogClose() {
    // window.location.reload();
  }

  // 接收子组件的返回值
  childSendfun(res: string) {
    if (res !== 'copyItem') {
      this.modQuestion(res)
    } else {
      window.location.reload()
    }
  }

  // 根据问卷ID获取章节，如没有第二章节则新建第二章节
  qryByQuestion(questionId: number | string): void {
    ajaxNew(
      '/business/api.v1.question/qpart/qryByQuestion',
      {
        questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const partItem = res.data.items.filter((item) => {
            return item.position === 'second'
          })
          // console.log("partItem", partItem);
          if (!partItem.length) {
            this.addPart()
          } else {
            // 有章节则使用
            this.partModifyForm.partName = partItem[0].partName
            this.partModifyForm.partNo =
              this.qsCreateForm.partNo =
              this.lastUplaod.partNo =
              this.lastInput.partNo =
                partItem[0].partNo
            this.qryQuestion(partItem[0].partNo)
          }
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 新建章节
  addPart(): void {
    ajaxNew('/business/api.v1.question/qpart/add', this.partCreateForm, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.partModifyForm.partNo =
            this.qsCreateForm.partNo =
            this.lastUplaod.partNo =
            this.lastInput.partNo =
              res.data.partNo
          this.handleCreateQs(this.lastUplaod)
          this.handleCreateQs(this.lastInput)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 修改章节
  modPart(type: string): void {
    if (!this.lastUplaod.questionTitle && type === 'all') {
      this['$message'].error('文件上传题标题不能为空')
    } else if (!this.lastInput.questionTitle && type === 'all') {
      this['$message'].error('填空题标题不能为空')
    } else {
      if (this.qsList.length) {
        this.qsList.forEach((item) => {
          this.partModifyForm.subjectItem.push({
            subjectId: item.subjectId,
            subjectNo: item.subjectNo,
            sortShow: item.sortShow,
          })
        })
      }
      if (type === 'all') this.handleSaveLast()
      const loading = this['$loading']({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      ajaxNew(
        '/business/api.v1.question/qpart/mod',
        this.partModifyForm,
        'post'
      )
        .then((res) => {
          if (res.resultCode === '0') {
            this.partModifyForm.partNo = this.qsCreateForm.partNo =
              res.data.partNo
            this['$message']({
              message: '保存成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                if (type === 'all') {
                  this['$router'].push(
                    '/step4?id=' + this.partCreateForm.questionId
                  )
                } else if (type === 'prev') {
                  this['$router'].push(
                    '/step2?id=' + this.partCreateForm.questionId
                  )
                }
              },
            })
          } else {
            this['$message'].error(res.resultMsg)
          }
          loading.close()
        })
        .catch((e) => {
          this['$message'].error(e)
          loading.close()
        })
    }
  }

  // 根据章节查询题目
  qryQuestion(partNo: string): void {
    ajaxNew('/business/api.v1.question/qsubject/qryAll', { partNo }, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.setQsListRefresh(false)
          this.qsList = []
          res.data.items.forEach((item) => {
            // 文件上传题目过滤掉
            if (item.questionType === 6) {
              this.lastUplaod = Object.assign(this.lastUplaod, item)
            } else if (item.questionType === 1) {
              // 填空题过滤掉
              this.lastInput = Object.assign(this.lastInput, item)
            } else {
              this.qsList.push(item)
            }
          })
          // 如果存在活动的索引，则同步最新数据
          if (this.activeIndex) {
            this.qsItem = this.qsList[this.activeIndex - 1]
          }
          this.modPart('title')
          // 历史问卷没有创建过填空题的时候
          const hasInputQs = res.data.items.some((v) => {
            return v.questionType === 1
          })
          if (!hasInputQs) this.handleCreateQs(this.lastInput)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 获取测试项目数量
  getTextItemNum(subjectNo, item): void {
    ajaxNew(
      '/business/api.v1.question/qitem/qrySum',
      {
        subjectNo,
        questionId: this['$route'].query.id,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          const relList = res.data.items
          relList.forEach((v1) => {
            this.qsList.forEach((v2) => {
              if (v2.options) {
                v2.options.forEach((v3) => {
                  if (v1.optionNo === v3.optionNo) {
                    v3.textNum = v1.relateId
                  }
                })
              }
            })
          })
          this.qsList = JSON.parse(JSON.stringify(this.qsList))
          this.cardDialog.visible = true
          this.qsItem = item
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 创建问题
  handleCreateQs(param?): void {
    let params = {
      sortShow: 0,
    }
    if (!param) {
      params = this.qsCreateForm
    } else {
      if (param.questionType === 6) {
        params = this.lastUplaod
      } else {
        params = this.lastInput
      }
    }
    params.sortShow = this.qsList.length + 1
    ajaxNew('/business/api.v1.question/qsubject/add', params, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          const obj = {
            questionType: 3,
            partNo: res.data.partNo,
            subjectNo: res.data.subjectNo,
            subjectId: res.data.subjectId,
            isEdit: true,
          }

          this.qsList.push(obj)
          this.qsList.forEach((item, index) => {
            item.sortShow = index + 1
          })
          this.qryQuestion(this.qsCreateForm.partNo)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 编辑题目
  modQuestion(params, source?: string): void {
    if (params.optionItem && params.optionItem.length) {
      params.optionItem.forEach((p, index) => {
        p.sortShow = index + 1
      })
    }
    ajaxNew('/business/api.v1.question/qsubject/mod', params, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.cardDialog.visible = false
          if (source) {
            //   this["$message"]({
            //     message: "保存成功",
            //     type: "success",
            //     duration: 500,
            //     onClose: () => {
            //       this.qryQuestion(this.qsCreateForm.partNo);
            //     },
            //   });
            // } else {
            //   this.qryQuestion(this.qsCreateForm.partNo);
          }
          this.qryQuestion(this.qsCreateForm.partNo)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 编辑12宫格
  handleCardEdit(item, index) {
    this.activeIndex = index
    this.getTextItemNum(item.subjectNo, item)
  }

  // 删除12宫格
  handleCardDel(qsItem): void {
    this.loading = true
    const param = {
      partNo: qsItem.partNo,
      subjectId: qsItem.subjectId,
      subjectNo: qsItem.subjectNo,
    }
    // this["$confirm"](`您确认删除服务项目《${qsItem.questionTitle}》？`)
    this['$confirm'](`您确认删除服务项目？`)
      .then(() => {
        ajaxNew('/business/api.v1.question/qsubject/del', param, 'post')
          .then((res) => {
            if (res.resultCode === '0') {
              this.qryQuestion(this.partModifyForm.partNo)
            } else {
              this['$message'].error(res.resultMsg)
            }
            this.loading = false
          })
          .catch((e) => {
            this['$message'].error(e)
            this.loading = false
          })
      })
      .catch((e) => {
        console.log(e)
        this.loading = false
      })
  }

  // 复制9宫格
  handleCardCopy(item): void {
    const loading = this['$loading']({
      lock: true,
      text: '项目复制中,请稍后...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const param = {
      partNo: item.partNo,
      subjectNo: item.subjectNo,
      subjectId: item.subjectId,
      questionTitle: `${item.questionTitle}（复制）`,
      sortShow: item.sortShow + 1,
    }
    ajaxNew('/business/api.v1.question/qsubject/copy', param, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.qryQuestion(this.partModifyForm.partNo)
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  // 点击关联逻辑按钮
  handleRelevanceButton(item) {
    this.qsRelevanceDialog = true
    item.source = 'card'
    this.qsRelevanceParam = item
  }

  childSendQsfun(res): void {
    if (res) {
      this.qsRelevanceDialog = false
      // window.location.reload();
    }
  }

  // 保存最后文件上传题目
  handleSaveLast(source?: string) {
    this.modQuestion(this.lastUplaod, source)
    this.modQuestion(this.lastInput, source)
  }

  // 从其他问卷复制
  handleCopyOther(): void {
    this.otherModal.isShow = true
  }
  // 搜索全部问卷
  handleSearch(): void {
    ajaxNew(
      '/business/api.v1.question/question/qryInfo',
      this.paginationParam,
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          res.data.items.forEach((i) => {
            i.items = []
          })
          this.questionList = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 展开某一行
  handleExpand(row, expandedRows): void {
    // console.log(row, expandedRows);
    // todo 收起也请求了接口
    if (expandedRows.length) this.qryByPosition(row)
  }
  // 点击整行
  handleClickRow(row, column, event): void {
    const parentnNode = event.target.parentNode.parentNode.className
    const node: any = this.$refs['table']
    node.toggleRowExpansion(row, !parentnNode.includes('expanded'))
  }
  // 获取问题对应的9宫格数据（根据章节位置查询题目）
  qryByPosition(row): void {
    qryByPositionAction({
      position: 'second',
      questionId: row.questionId,
    }).then((res) => {
      row.items = res.filter((v) => {
        return v.questionType !== 6 && v.questionType !== 1
      })
    })
  }
  // 复制其他问卷的选项
  handleCopyItem(questionId, item): void {
    const loading = this['$loading']({
      lock: true,
      text: '选项复制中,请稍后...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    ajaxNew(
      '/business/api.v1.question/qsubject/copyOther',
      {
        partNo: this.qsCreateForm.partNo,
        subjectNo: item.subjectNo,
        subjectId: item.subjectId,
        questionId,
        questionTitle: `${item.questionTitle}（复制）`,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message'].success('复制成功')
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  // 关闭弹窗
  handleCloseOther(): void {
    this.qryQuestion(this.qsCreateForm.partNo)
  }

  // 计算属性（computed）
  get getQslistRefersh(): boolean {
    return this['$store'].state.qsListRefresh
  }

  // 启用问卷 tood
  qsSetState() {
    ajaxNew(
      '/business/api.v1.question/question/modState',
      {
        questionId: this['$route'].query.id,
        state: 1,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          console.log('')
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  mounted(): void {
    this.partModifyForm.questionId =
      this.partCreateForm.questionId =
      this.lastUplaod.questionId =
      this.lastInput.questionId =
        this['$route'].query.id as string

    if (this['$route'].query.id) {
      this.qryByQuestion(this['$route'].query.id as string)
    }
  }
}
export default Step3
</script>

<style>
.el-table .cell {
  cursor: pointer;
}
.el-form.demo-table-expand {
  clear: both;
}
.card_title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 50px;
  line-height: 50px;
  widows: 100%;
}
</style>
<style lang="less" scoped>
.card_wrap {
  .card_box {
    width: 333px;
    height: 125px;
    float: left;
    margin: 10px 10px 0 0;

    .card_title {
      text-align: center;
      font-size: 16px;
      height: 50px;
      line-height: 50px;
    }
    .card_button {
      text-align: right;

      .el-button-group {
        display: flex;
      }
    }
  }
  .card_box_add {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>