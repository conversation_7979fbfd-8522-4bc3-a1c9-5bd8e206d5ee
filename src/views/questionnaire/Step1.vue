<template>
  <el-row class="step">
    <el-col :span="24">
      <h2>Step1：选择包含商品类目</h2>
      <p>商品类目不可重复，关联问题的商品不会展示</p>
    </el-col>
    <el-col :span="24">
      <el-form :model="ruleForm"
               label-width="100px">
        <el-form-item label="名称配置">
          <el-input v-model="questionFrom.questionName"
                    maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="所属BU">
          <el-select v-model="questionFrom.bu"
                     filterable
                     placeholder="请选择所属BU..."
                     @change="changeBu">
            <el-option v-for="item in buList"
                       :key="item.id"
                       :label="item.buName"
                       :value="item.bu"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属业务线">
          <el-select v-model="questionFrom.itemId"
                     filterable
                     placeholder="请选择所属业务线...">
            <el-option v-for="item in businessList"
                       :key="item.configId"
                       :label="item.configName"
                       :value="item.configId">{{item.configName}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属分类">
          <el-cascader style="width: 100%"
                       placeholder="请选择..."
                       :options="options"
                       :props="props"
                       filterable
                       clearable
                       :show-all-levels="true"
                       v-model="defaulValue"
                       ref="cascader"
                       @change="handleChangeCategory"></el-cascader>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="handleSbmit">下一步，配置首页问题</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script lang='ts'>
import { Vue, Component } from 'vue-property-decorator'
import { ajaxNew } from './../../api/index'
import { createTreeData, local } from './../../assets/javascript/util'
import { qryInfoByLab } from './../../api/common'

@Component({
  name: 'Step1',
})
class Step1 extends Vue {
  constructor(p) {
    super(p)
  }
  private ruleForm = {}
  private formItem = {
    catagoryPath: '',
    questionId: 0,
  }
  private value = ''
  private buList = []
  private businessList = []
  private options = []
  private props = {
    multiple: true,
    label: 'catagoryName',
    value: 'catagoryId',
    emitPath: false,
  }
  private questionFrom = {
    bu: '',
    questionId: '',
    questionName: '',
    itemId: '',
    catagoryItem: [],
  }
  private defaulValue = []
  private categorys = []
  private system = 'ticCenter'

  changeBu(val: string): void {
    this.businessList = []
    this.questionFrom.itemId = ''
    this.getBusinessList(val)
  }

  handleSbmit(): void {
    const node: any = this.$refs['cascader']
    const nodes = node.getCheckedNodes()
    // 如果已选节点为空，尝试通过ref再次获取，并生成数据
    if (!this.questionFrom.catagoryItem.length && nodes.length) {
      nodes.forEach((item) => {
        this.questionFrom.catagoryItem.push({ catagoryId: item.value })
      })
    }
    if (
      this.questionFrom.bu &&
      this.questionFrom.itemId &&
      this.questionFrom.questionName
    ) {
      if (this['$route'].query.id) {
        this.modifyQuestion()
      } else {
        this.createQuestion()
      }
    } else {
      this['$message'].error('名称配置、所属BU、所属业务线都不能为空')
    }
  }

  handleChangeCategory(data): void {
    this.questionFrom.catagoryItem = []
    data.forEach((item) => {
      const catagoryId = { catagoryId: item }
      this.questionFrom.catagoryItem.push(catagoryId)
    })
  }

  getBuList(): void {
    ajaxNew('/business/api.v1.center/bu/qryBySystem', {}, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          // this.buList = res.data.items.filter((v) => {
          //   return v.parentCode !== "0";
          // });
          this.buList = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  getCategoryList(): void {
    ajaxNew('/business/api.v1.center/catagory/qryUnuse', this.formItem, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          if (this['$route'].query.id) {
            this.options = res.data.items
            this.qryQuestionDetail(this['$route'].query.id)
          } else {
            this.options = createTreeData(
              res.data.items,
              'catagoryId',
              'parentId'
            )
          }
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  createQuestion(): void {
    const loading = this['$loading']({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    ajaxNew(
      '/business/api.v1.question/question/save',
      this.questionFrom,
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$router'].push('/step2?id=' + res.data.questionId)
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  modifyQuestion(): void {
    const loading = this['$loading']({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    this.questionFrom.questionId = this['$route'].query.id as string
    ajaxNew('/business/api.v1.question/question/mod', this.questionFrom, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this['$router'].push('/step2?id=' + this['$route'].query.id)
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  qryQuestionDetail(id): void {
    ajaxNew(
      '/business/api.v1.question/question/qryDtl',
      {
        questionId: id,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.questionFrom.bu = res.data.bu
          this.questionFrom.itemId = res.data.itemId
          this.questionFrom.questionName = res.data.questionName
          // 再次生成树型数据（合并数组并去重）
          this.options = createTreeData(
            this.MergeArray(this.options, res.data.catagoryItem),
            'catagoryId',
            'parentId'
          )
          // 设置默认选中
          res.data.catagoryItem.forEach((item) => {
            this.defaulValue.push(item.catagoryId)
          })
          // 生成预览category
          this.createCategorys(res.data.catagoryItem)
          if (this.questionFrom.bu) this.getBusinessList(this.questionFrom.bu)
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  createCategorys(datas): void {
    const parendNodeIndex = []
    datas.forEach((item, index) => {
      if (!item.parentId) parendNodeIndex.push(index)
    })
    if (parendNodeIndex.length > 1) {
      datas.forEach((item, index) => {
        if (index < parendNodeIndex[1]) this.categorys.push(item.catagoryId)
      })
    } else {
      datas.forEach((item) => {
        this.categorys.push(item.catagoryId)
      })
    }
    local.set('category', this.categorys.join(','))
  }

  MergeArray(arr1, arr2) {
    const _arr = []
    for (let i = 0; i < arr1.length; i++) {
      _arr.push(arr1[i])
    }
    for (let i = 0; i < arr2.length; i++) {
      let flag = true
      for (let j = 0; j < arr1.length; j++) {
        if (arr2[i].catagoryId == arr1[j].catagoryId) {
          flag = false
          break
        }
      }
      if (flag) {
        _arr.push(arr2[i])
      }
    }
    return _arr
  }

  getBusinessList(bu: string): void {
    qryInfoByLab({
        bu,
        configType: 100000,
        system: this.system
      }).then(res => {
        Object.values(res).forEach((v) => {
            this.businessList = this.businessList.concat(v)
          })
      })
  }

  mounted() {
    this.getCategoryList()
    this.getBuList()
  }
}

export default Step1
</script>
