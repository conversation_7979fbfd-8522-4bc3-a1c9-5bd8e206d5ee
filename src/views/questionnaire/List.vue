<template>
  <el-row>
    <el-col :span="10">
      <el-input clearable
                @change='getQustionList'
                v-model="paginationParam.questionName"
                placeholder="请输入问卷名称..."
                style="width: 300px; margin-right: 10px"></el-input>
      <el-select placeholder="请选择业务线..."
                 style="width: 300px; margin-right: 10px"
                 v-model="paginationParam.itemId"
                 clearable>
        <el-option v-for='(item, index) of buinessLine'
                   :key='index'
                   :label="item.configName"
                   :value="item.configId">
        </el-option>
      </el-select>
      <el-button @click="getQustionList">搜索</el-button>
    </el-col>
    <el-col :span="14"
            style="text-align: right;">
      <el-button @click="handleCreateQuestion">新建问卷</el-button>
      <el-button @click="handleCategoryCatalog">查看所有产品类目</el-button>
    </el-col>
    <el-col :span="24">
      <el-row class="item-box"
              v-for="item in questionList"
              :key="item.questionId">
        <el-col :span="24">
          <div class="item-box-option">
            <span>{{ item.questionName }}</span>
            <span>{{ item.buName }}</span>
            <el-switch v-model="item.state"
                       :active-value="1"
                       :inactive-value="0"
                       active-text="启用"
                       inactive-text="暂停"
                       @change="handleChangeState(item)"></el-switch>
            <span>{{ item.state ? "启用中" : '未启用'}}</span>
          </div>
          <el-button-group>
            <el-button icon="el-icon-"
                       @click="handleRelateItem(item)">配置项目</el-button>
            <el-button icon="el-icon-view"
                       @click="handlePreviw(item)">预览</el-button>
            <el-button icon="el-icon-s-grid"
                       @click="handleCopy(item)">复制模板</el-button>
            <el-button icon="el-icon-edit"
                       @click="handleEdit(item)">编辑</el-button>
            <el-button icon="el-icon-delete"
                       @click="handleDel(item)">删除</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="24">
          <div class="item-box-category">
            包含产品类目：
            <el-select value
                       filterable
                       clearable
                       placeholder="输入关键字查询..."
                       v-model="item.model">
              <template v-for="(category, index) in item.catagoryItem">
                <el-option :key="index"
                           :label="category.catagoryPath"
                           :value="category.catagoryId"
                           v-if="category.questionName"></el-option>
              </template>
            </el-select>
            <!-- <el-cascader
              style="width: 100%"
              placeholder="请选择..."
              :options="item.options"
              :props="props"
              filterable
              clearable
              :show-all-levels="true"
              ref="cascader"
              @change="handleChangeCategory"
            ></el-cascader> -->
          </div>
          <div class="item-box-info">
            <div>最后编辑于：{{ item.stateDate }}</div>
            <div>编辑人：{{ item.operatorCode }}</div>
          </div>
        </el-col>
        <el-col></el-col>
      </el-row>
    </el-col>
    <el-col>
      <div class="pagination">
        <el-pagination background
                       layout="prev, pager, next"
                       :total="paginationTotal"
                       @size-change="handlePageChange"
                       @current-change="handlePageChange"></el-pagination>
      </div>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { ajaxNew } from './../../api/index'
import { local } from './../../assets/javascript/util'
import { qryBusiness } from './../../api/common'

@Component
class QuestionnaireList extends Vue {
  private questionList = []
  private categoryValue = ''
  private paginationParam = {
    questionName: '',
    itemId: '',
    pageNum: 1,
    pageRow: 10,
  }
  private paginationTotal = 0
  private options = []
  private props = {
    multiple: true,
    label: 'catagoryName',
    value: 'catagoryId',
    emitPath: false,
  }
  private categorys = []
  private loading = false
  private system = 'ticCenter'
  private buinessLine = []

  // 预览跳转
  handlePreviw(item): void {
    this.createCategorys(item.catagoryItem)
    window.open('/preview?category=' + this.categorys.join(','))
  }

  // 生成预览category
  createCategorys(datas) {
    const parendNodeIndex = []
    datas.forEach((item, index) => {
      if (!item.parentId) parendNodeIndex.push(index)
    })
    console.log(parendNodeIndex)
    if (parendNodeIndex.length > 1) {
      datas.forEach((item, index) => {
        if (index < parendNodeIndex[1]) this.categorys.push(item.catagoryId)
      })
    } else {
      datas.forEach((item) => {
        this.categorys.push(item.catagoryId)
      })
    }
    local.set('category', this.categorys.join(','))
  }

  // 关联测试项目
  handleRelateItem(item): void {
    this['$router'].push({
      path: '/testItem',
      query: {
        questionId: item.questionId,
      },
    })
  }

  handleCreateQuestion() {
    this['$router'].push('/step1')
  }

  handleCategoryCatalog() {
    this['$router'].push('/category')
  }
  handleChangeCategory(data) {
    console.log(data)
    // this.questionFrom.catagoryItem = [];
    // data.forEach(item => {
    //   const catagoryId = { catagoryId: item };
    //   this.questionFrom.catagoryItem.push(catagoryId);
    // });
  }

  // 获取问题列表
  getQustionList() {
    ajaxNew(
      '/business/api.v1.question/question/qry',
      this.paginationParam,
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          // res.data.items.forEach((item) => {
          //   item.model = "请选择..";
          // });
          // res.data.items.forEach((item) => {
          //   item.options = createTreeData(item.catagoryItem, 'catagoryId', 'parentId');
          // });
          this.questionList = res.data.items
          this.paginationTotal = res.data.totalNum
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  //
  handleChangeState(item) {
    ajaxNew(
      '/business/api.v1.question/question/modState',
      {
        questionId: item.questionId,
        state: item.state,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          if (item.state) {
            this['$message'].success('问卷已启用')
          } else {
            this['$message'].warning('问卷已暂停')
          }
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 编辑问题
  handleEdit(item): void {
    this['$confirm'](`编辑问卷会自动停用问卷，是否继续？`)
      .then(() => {
        this.createCategorys(item.catagoryItem)
        window.open(`/step1?id=${item.questionId}`)
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 删除问题
  handleDel(item) {
    // this["$confirm"](`您确认删除问卷《${item.questionName}》？`)
    this['$confirm'](`您确认删除问卷？`)
      .then(() => {
        ajaxNew(
          '/business/api.v1.question/question/del',
          {
            questionId: item.questionId,
          },
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this['$message'].success('删除成功')
              this.getQustionList()
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            this['$message'].error(e)
          })
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 复制问题
  handleCopy(item) {
    const loading = this['$loading']({
      lock: true,
      text: '模板复制中,请稍后...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    ajaxNew(
      '/business/api.v1.question/question/copy',
      {
        questionName: item.questionName + '（复制）',
        questionId: item.questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.getQustionList()
        } else {
          this['$message'].error(res.resultMsg)
        }
        loading.close()
      })
      .catch((e) => {
        this['$message'].error(e)
        loading.close()
      })
  }

  // 切换分页
  handlePageChange(page: number): void {
    this.paginationParam.pageNum = page
    this.getQustionList()
  }

  mounted() {
    this.getQustionList()
    qryBusiness({ configType: 100000, system: this.system }).then((res) => {
      debugger
      this.buinessLine = res
    })
  }
}
export default QuestionnaireList
</script>

<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>

<style lang="less" scoped>
.item-box {
  border: 1px solid #ccc;
  padding: 10px;
  margin-top: 20px;
  background: #fff;
  box-shadow: 0 0 3px #ccc;
  border-radius: 5px;

  .item-box-option {
    float: left;
    * {
      padding-right: 15px;
    }
  }
  .item-box-category {
    float: left;
  }
  .item-box-info {
    float: right;
    display: flex;
    justify-content: end;
  }
}
.el-button-group {
  float: right;
}
</style>