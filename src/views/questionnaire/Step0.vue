<template>
  <el-row class="step"
          v-loading='loading'
          element-loading-text="Loading..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)">
    <el-col :span="24">
      <h2>流程配置</h2>
      <div>
        当客户所选商品类目为：{{ routerQuery.categoryName }}
      </div>
    </el-col>
    <el-col :span="24">
      <el-container>
        <el-aside width="170px"
                  class="aside">
          <el-button v-for="item of buttonGroup"
                     :key="item.type"
                     :disabled="!item.status">{{item.name}}</el-button>
        </el-aside>
        <el-main>
          <el-col :span="24">
            <el-form ref="form"
                     :model="queryParams"
                     label-width="100px">
              <el-form-item label="本页标题">
                <el-input disabled
                          maxlength="50"
                          v-model="queryParams.partName"
                          placeholder="请输入本页标题..."
                          style="width: 300px; margin-right: 15px;"></el-input>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <div v-for="(qsItem, index) of qsList"
                 :key="qsItem.subjectId"
                 class="qs_list">
              <template v-if="qsItem.questionType === 10">
                <UploadAI :childParams="qsItem"
                          :labels="labels"
                          @modQuestion="modQuestion" />
              </template>
              <template v-if="qsItem.questionType === 51">
                <AI :childParams="qsItem"
                    :labels="labels"
                    @modQuestion="modQuestion" />
              </template>
              <div style="text-align: right;">
                <el-button-group>
                  <el-button icon="el-icon-edit"
                             @click="handleEditQuestion(qsItem)"
                             :disabled="qsItem.isEdit">编辑</el-button>
                </el-button-group>
              </div>
            </div>
          </el-col>
          <el-col :span="24"
                  style="text-align: center; margin-top: 20px;">
            <el-button type="primary"
                       @click="handleSave">保存</el-button>
            <el-button @click="handleCancle">取消</el-button>
            <el-button v-if="partInfo.partNo"
                       @click="handelSetState">{{ partInfo.state ? '失效' : '激活' }}</el-button>
          </el-col>
        </el-main>
      </el-container>
    </el-col>
  </el-row>
</template>
  
<script lang='ts'>
import { Component, Vue, Watch } from 'vue-property-decorator'
import {
  buttonGroupStep0,
  generateRandomString,
} from './../../assets/javascript/util'
import UploadAI from './../../components/question/UploadAI.vue'
import AI from './../../components/question/AI.vue'
import {
  qryFlow,
  flowSetState,
  flowEdit,
  flowAdd,
  qryTag,
} from './../../api/common'

// 定义流程数据接口
interface IFlowSubject {
  subjectId?: string
  subjectNo?: string
  questionTitle?: string
  questionType?: number
  sortShow?: number
  options?: any[]
  // 其他可能的字段
}

interface IFlowRelate {
  relateId?: string
  relateType?: string
  sourceId?: string
  targetId?: string
  // 其他可能的字段
}

interface IFlowAddParams {
  partId?: string
  partNo?: string
  partName?: string
  position?: string
  businessType?: string
  businessCode?: string
  subjects?: IFlowSubject[]
  relates?: IFlowRelate[]
}

@Component({
  name: 'Step0',
  components: {
    UploadAI,
    AI,
  },
})
class Step0 extends Vue {
  // 按住组(1:填空,2:单选,3:多选,4:下拉框单选,5:下拉框多选,6:上传)
  private buttonGroup = buttonGroupStep0
  // 题目列表
  private qsList = []
  private loading: boolean = false
  private routerQuery = {
    businessId: '',
    businessType: '',
    categoryName: '',
  } as any
  private queryParams: IFlowAddParams = {
    partId: '',
    partNo: '',
    partName: '',
    position: 'step0',
    businessType: 'category',
    businessCode: '',
    subjects: [],
    relates: [], // 如果有关联关系数据，可以在这里添加
  }
  private labels = []
  private partNo = ''
  mounted(): void {
    qryTag({}).then((res) => {
      this.labels = res
    })
    // 全局生成一个partrNo
    this.partNo = generateRandomString(16)
    this.queryParams.partNo = this.partNo
    const { businessId, categoryName } = this['$route'].query as any
    this.routerQuery = {
      businessId,
      categoryName,
    }
    this.queryParams.partName = categoryName
    this.queryParams.businessCode = businessId
    this.qryFlowFun(businessId)
  }
  // 编辑题目
  handleEditQuestion(qsItem): void {
    qsItem.isEdit = !qsItem.isEdit
    this.qsList = JSON.parse(JSON.stringify(this.qsList))
  }
  private partInfo = {} as any
  private async qryFlowFun(businessId: string) {
    qryFlow({
      position: 'step0',
      businessType: 'category',
      businessCode: businessId,
    }).then((res) => {
      if (!res.length) {
        // 新增,生成2道题目
        this.qsList = [
          {
            questionTitle: '',
            sortShow: 1,
            uploadType: 3,
            partNo: this.partNo,
            questionType: 10,
            subjectNo: generateRandomString(16),
            isEdit: true,
            options: [],
          },
          {
            questionTitle: 'AI组件',
            sortShow: 2,
            partNo: this.partNo,
            questionType: 51,
            subjectNo: generateRandomString(16),
            isEdit: true,
            aiModel: 'GPT-4o',
            options: [],
          },
        ]
      } else {
        this.partInfo = res[0]
        if (this.partInfo.partNo) {
          this.queryParams.partNo = this.partInfo.partNo
          // 编辑
          this.partInfo.subjects.forEach((v) => {
            v.isEdit = true
          })
          this.qsList = this.partInfo.subjects
          // 确保视图更新
          this.qsList = [...this.qsList]
        }
      }
    })
  }

  async handleSave() {
    // 保存前先判断是否都已保存qsList isEdit均为false
    if (this.qsList.some((item) => item.isEdit)) {
      this.$message.warning('请先完成编辑')
      return
    }
    // 根据qsList中的数据生成关联关系数据
    /* 
        businessType		
        businessCode		
        partNo		
        subjectNo 第一题信息		
        optionNo		
        relatePartNo		
        relateSubjectNo	第二题信息
    */
    const releate = {
      businessType: 'category',
      businessCode: this.routerQuery.businessId,
      partNo: this.partNo,
      relatePartNo: this.partNo,
      subjectNo: this.qsList[0].subjectNo,
      relateSubjectNo: this.qsList[1].subjectNo,
    } as any
    this.queryParams.relates = [releate]
    // 排序 this.qsList sortShow
    this.qsList.forEach((item, index) => {
      item.sortShow = index + 1
    })
    this.queryParams.subjects = this.qsList
    // 合并2份数据
    this.queryParams.partId = this.partInfo.partId
    this.loading = true
    if (!this.partInfo.partId) {
      flowAdd(this.queryParams).then((res) => {
        if (res) {
            this.handleCancle()
        }
      })
    } else {
      flowEdit(this.queryParams).then((res) => {
        if (res) {
          setTimeout(() => {
           this.handleCancle()
          }, 3000);
        }
      })
    }
  }

  // 接收子组件返回的值
  modQuestion(res: any) {
    // 所有的options下 subjectNo赋同一个值
    res.options.forEach((item, index) => {
      item.sortShow = index + 1
      item.subjectNo = res.subjectNo
    })
    // 根据subjectNo相同做更新
    if (res && res.subjectNo) {
      const index = this.qsList.findIndex(
        (item: any) => item.subjectNo === res.subjectNo
      )
      if (index !== -1) {
        // 使用Vue的数组更新方法确保响应式更新
        this.$set(this.qsList, index, res)
        console.log('题目已更新:', res.subjectNo)
      } else {
        console.warn('未找到匹配的题目:', res.subjectNo)
      }
      // 确保视图更新
      this.qsList = [...this.qsList]
    }
  }

  // 取消返回类目选择页面
  handleCancle() {
    this['$router'].push('/category')
  }

  // 设置章节状态
  handelSetState() {
    if (!this.partInfo.partNo) return
    flowSetState({
      partNo: this.partInfo.partNo,
      businessCode: this.partInfo.businessCode,
      state: this.partInfo.state ? 0 : 1,
    }).then((res) => {
      this.partInfo.state = this.partInfo.state ? 0 : 1
      this['$message'].success('状态设置成功')
    })
  }
}
export default Step0
</script>
  
  <style lang="less" scoped>
.category_box {
  height: auto;
  overflow: hidden;
  div {
    height: 25px;
    line-height: 25px;
  }
}
.el-container {
  border-top: 1px solid #ccc;
  margin-top: 15px;
  min-height: 100vh;

  .el-aside {
    border-right: 1px solid #ccc;
    text-align: center;

    .el-button {
      margin-top: 20px;
    }
  }
}
.qs_list {
  border: 1px solid #ddd;
  box-shadow: 5px 5px 3px #ddd;
  border-radius: 5px;
  margin-top: 20px;
  padding: 20px;
}
</style>