<template>
  <el-row>
    <h2 style="font-size: 16px; padding-bottom: 15px; font-weight: normal;">已关联项目</h2>
    <el-col :span="24">
      <el-table :data="itemList"
                border
                style="width: 100%"
                size="small">
        <el-table-column prop="itemId"
                         label="ID"
                         width="115"></el-table-column>
        <el-table-column prop="businessLine"
                         label="业务线"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.businessLine">
              <span class="show_tooltip">{{scope.row.businessLine}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="itemName"
                         label="名称"
                         width="150">
          <template slot-scope="scope">
            <!-- <el-tooltip :content="scope.row.itemName">
              <span class="show_tooltip">{{scope.row.itemName}}</span>
            </el-tooltip> -->
            <div v-html='scope.row.itemName'></div>
          </template>
        </el-table-column>
        <el-table-column prop="itemAlias"
                         label="别名"
                         width="150">
          <template slot-scope="scope">
            <!-- <el-tooltip :content="scope.row.itemAlias">
              <span class="show_tooltip">{{scope.row.itemAlias}}</span>
            </el-tooltip> -->
            <div v-html='scope.row.itemAlias'></div>
          </template>
        </el-table-column>
        <el-table-column prop="standardCode"
                         label="标准"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.standardCode">
              <div slot="content">
                <div v-for="(name, index) of scope.row.standardCode.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.standardCode}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="labelName"
                         label="项目标签"
                         width="120"></el-table-column>
        <el-table-column prop="testMemo"
                         label="测试备注"
                         width="150">
          <template slot-scope="scope">
            <!-- <el-tooltip :content="scope.row.testMemo">
              <div slot="content">
                <div v-for="(name, index) of scope.row.testMemo.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.testMemo}}</span>
            </el-tooltip> -->
            <div v-html='scope.row.testMemo'></div>
          </template>
        </el-table-column>
        <el-table-column prop="sampleRequirements"
                         label="样品要求"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.sampleRequirements">
              <div slot="content">
                <div v-for="(name, index) of scope.row.sampleRequirements.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.sampleRequirements}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <!--<el-table-column label="用户可选"
                         width="150">
          <template slot-scope="scope">
            {{scope.row.isOptional ? '可选' : '不可选'}}
          </template>
        </el-table-column>-->
        <el-table-column prop="testDays"
                         label="TAT"
                         width="50"></el-table-column>
        <el-table-column label="执行实验室"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.labName">
              <div slot="content">
                <div v-for="(labItemm,labIndex) of scope.row.labName.split(',')"
                     :key="labIndex">{{labItemm}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.labName}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="price"
                         label="价格"
                         width="100">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.price + '/' + scope.row.unit">
              <span class="show_tooltip">{{ scope.row.price }}{{`/${scope.row.unit}`}}</span>
              <!-- <span class="show_tooltip" v-if="scope.row.unit"></span> -->
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="cnasLab"
                         label="CNAS资质"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.cnasLab">
              <div slot="content">
                <div v-for="(name, index) of scope.row.cnasLab.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.cnasLab}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="cmaLab"
                         label="CMA资质"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.cmaLab">
              <div slot="content">
                <div v-for="(name, index) of scope.row.cmaLab.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.cmaLab}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="memo"
                         label="其他备注(内部可见)"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.memo">
              <div slot="content">
                <div v-for="(name, index) of scope.row.memo.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.memo}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="personCode"
                         label="最后更新人"
                         width="120"></el-table-column>
        <el-table-column prop="stateDate"
                         label="更新时间"
                         width="140"></el-table-column>
        <el-table-column label="操作"
                         width="100px"
                         fixed="right">
          <template slot-scope="scope">
            <el-button-group>
              <el-button size="small"
                         icon="el-icon-delete"
                         @click="handleDel(scope.row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'

@Component({
  name: 'ReletaTestItem',
})
class ReletaTestItem extends Vue {
  @Mutation('setTestItemRefresh') setTestItemRefresh
  @Watch('getTestItemRefresh', { immediate: true, deep: true })
  watchTestItem(newVal) {
    if (newVal) {
      // console.log("再次请求列表--------------");
      this.setTestItemRefresh(false)
      if (this.partNo) {
        this.qryItemListGroup(this.questionId, this.groupNo)
      } else {
        this.qryItemList()
      }
    }
  }

  private itemList = []
  private qryItemParam = {
    questionId: '',
    optionNo: '',
    subjectNo: '',
  }
  private sysItem = []
  private partNo = ''
  private questionId = ''
  private groupNo = ''

  // 计算属性（computed）
  get getTestItemRefresh(): boolean {
    return this['$store'].state.testItemRefresh
  }

  handleDel(row) {
    this.sysItem = [
      {
        itemId: row.itemId,
      },
    ]
    this['$confirm'](`您确认删除？`)
      .then(() => {
        if (this.partNo) {
          this.delItemGroup()
        } else {
          this.delItem()
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 获取测试项目
  qryItemList(): void {
    ajaxNew('/business/api.v1.question/qitem/qry', this.qryItemParam, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.itemList = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 取消关联测试项目
  delItem(): void {
    ajaxNew(
      '/business/api.v1.question/qitem/unrelate',
      {
        questionId: this.qryItemParam.questionId,
        subjectNo: this.qryItemParam.subjectNo,
        optionNo: this.qryItemParam.optionNo,
        sysItem: this.sysItem,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '删除成功',
            onClose: () => {
              this.qryItemList()
            },
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 获取测试项目关联组
  qryItemListGroup(questionId, groupNo): void {
    ajaxNew(
      '/business/api.v1.question/qitem/qryByGroup',
      {
        groupNo,
        questionId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.itemList = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }
  // 取消关联测试项目组
  delItemGroup(): void {
    ajaxNew(
      '/business/api.v1.question/qitemrelate/del',
      {
        groupNo: this.groupNo,
        sysItem: this.sysItem,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '删除成功',
            onClose: () => {
              this.qryItemListGroup(this.questionId, this.groupNo)
            },
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  mounted() {
    this.qryItemParam.questionId = this.questionId = this['$route'].query
      .questionId as string
    this.qryItemParam.subjectNo = this['$route'].query.subjectNo as string
    this.qryItemParam.optionNo = this['$route'].query.optionNo as string
    this.partNo = this['$route'].query.partNo as string
    this.groupNo = this['$route'].query.groupNo as string
    if (this.partNo) {
      this.qryItemListGroup(this.questionId, this.groupNo)
    } else {
      this.qryItemList()
    }
  }
}
export default ReletaTestItem
</script>

<style lang='less' scoped>
.show_tooltip {
  width: 100%;
  height: 25px;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style>
.el-table--small td,
.el-table--small th {
  padding: 8px 0 !important;
}
</style>
