<template>
  <el-row>
    <el-form ref="form"
             :model="qryItemParam"
             label-width="100px"
             :inline="true"
             size="small">
      <el-col :span="8">
        <el-form-item label="ID">
          <el-input placeholder="请输入id..."
                    type="text"
                    v-model="qryItemParam.itemId"
                    clearable
                    @change="handleSearch"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="名称或别名">
          <el-input @change="handleSearch"
                    placeholder="请输入名称或别名..."
                    type="text"
                    v-model="qryItemParam.itemName"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="标准号">
          <el-input @change="handleSearch"
                    placeholder="请输入输入标准号..."
                    type="text"
                    v-model="qryItemParam.standardCode"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="业务线">
          <el-select clearable
                     placeholder="请选择业务线..."
                     filterable
                     v-model="qryItemParam.businessLine"
                     @blur="selectBlur" @change="handleBusinessLineChange">
            <el-option v-for="(item, index) of businessLineAll"
                       :key="index"
                       :label="item.configName"
                       :value="item.configName"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="样品分类">
          <el-select clearable
                     placeholder="请选择样品分类..."
                     filterable
                     multiple
                     v-model="qryItemParam.sampleCategoryCode"
                     @change="handleSampleCategoryChange">
            <el-option v-for="(item, index) of sampleCategoryList"
                       :key="index"
                       :label="item.categoryName"
                       :value="item.categoryCode"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="样品标准">
          <el-select clearable
                     placeholder="请选择样品标准..."
                     filterable
                     v-model="qryItemParam.sampleStandardCode"
                     @change="handleSearch">
            <el-option label="请选择..." value=""></el-option>
            <el-option v-for="(item, index) of sampleStandardList"
                       :key="index"
                       :label="item.standardName"
                       :value="item.standardCode"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="CNAS资质">
          <el-input @change="handleSearch"
                    placeholder="请输入输入CNAS资质..."
                    type="text"
                    v-model="qryItemParam.cnasLab"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="CMA资质">
          <el-input @change="handleSearch"
                    placeholder="请输入输入CMA资质..."
                    type="text"
                    v-model="qryItemParam.cmaLab"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="测试备注">
          <el-input @change="handleSearch"
                    placeholder="请输入输入测试备注..."
                    type="text"
                    v-model="qryItemParam.testMemo"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="最后更新人">
          <el-input @change="handleSearch"
                    placeholder="请输入最后更新人..."
                    type="text"
                    v-model="qryItemParam.personCode"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="实验室">
          <el-select clearable
                     multiple
                     placeholder="请选择实验室..."
                     v-model="qryItemParam.labNames"
                     @change="chanegLabAll">
            <el-option v-for="(item, index) of laboratoryListAll"
                       :key="index"
                       :label="item.labName"
                       :value="item.labName"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="其他备注">
          <el-input @change="handleSearch"
                    placeholder="请输入其他备注..."
                    type="text"
                    v-model="qryItemParam.memo"
                    clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="更新时间">
          <el-date-picker v-model="qryItemParam.stateDateArray"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="24"
              style="text-align: right; margin-top: 10px;">
        <el-button size="small"
                   type="primary"
                   @click="handleSearch">查询</el-button>
      </el-col>
    </el-form>
    <el-col :span="24">
      <div style="display: flex; justify-content: space-between;">
        <el-button-group>
          <el-button type="primary"
                     size="small"
                     icon="el-icon-connection"
                     @click="relateItem"
                     :disabled="isQuestionList">添加关联</el-button>
          <el-button type="primary"
                     size="small"
                     icon="el-icon-plus"
                     @click="handleAdd">新增</el-button>
          <el-button type="primary"
                     size="small"
                     icon="el-icon-delete"
                     @click="handleDel">删除</el-button>
        </el-button-group>
        <div style="display: flex;">
          <el-upload :on-success="onSuccess"
                     :show-file-list="uploadFile.showFileList"
                     :action="uploadFile.host + 'ticCenter/business/api.v0.platform/fileUpload/uploadOss'">
            <el-button type="primary"
                       size="small"
                       icon="el-icon-folder-add">新项目导入</el-button>
          </el-upload>
          <el-button-group>
            <el-button type="primary"
                       size="small"
                       icon='el-icon-download'
                       @click="handleExport">数据导出</el-button>
            <el-button :disabled='!modifyDialogTable.length'
                       type="primary"
                       size="small"
                       icon='el-icon-edit'
                       @click="handlemMltipleModify">批量修改</el-button>
          </el-button-group>
          <a href="/QMS测试项目导入模板.xlsx"
             download="/QMS测试项目导入模板.xlsx"
             class="download">下载导入表格</a>
        </div>
      </div>
    </el-col>
    <el-col :span="24">
      <el-table :data="itemList"
                stripe
                border
                style="width: 100%; margin-top: 10px;"
                @select="tableSelect"
                @select-all="tableSelectAll"
                size="small">
        <el-table-column type="selection"
                         width="40"></el-table-column>
        <el-table-column prop="itemId"
                         label="ID"
                         width="70"></el-table-column>
        <el-table-column prop="businessLine"
                         label="业务线"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.businessLine">
              <span class="show_tooltip">{{scope.row.businessLine}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="itemName"
                         label="名称"
                         width="150">
          <template slot-scope="scope">
            <div v-html='scope.row.itemName'></div>
          </template>
        </el-table-column>
        <el-table-column prop="itemAlias"
                         label="别名"
                         width="150">
          <template slot-scope="scope">
            <div v-html='scope.row.itemAlias'></div>
          </template>
        </el-table-column>
        <el-table-column prop="itemTypeName"
                         label="项目类型"
                         width="120">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.itemTypeName">
              <span class="show_tooltip">{{scope.row.itemTypeName}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="sampleCategoryName"
                         label="样品分类"
                         width="120">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.sampleCategoryName">
              <span class="show_tooltip">{{scope.row.sampleCategoryName}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="sampleStandardName"
                         label="样品标准"
                         width="120">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.sampleStandardName">
              <span class="show_tooltip">{{scope.row.sampleStandardName}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="testLineId"
                         label="Testline ID"
                         width="120">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.testLineId">
              <span class="show_tooltip">{{scope.row.testLineId}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="standardCode"
                         label="标准"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.standardCode">
              <div slot="content">
                <div v-for="(name, index) of scope.row.standardCode.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.standardCode}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="labelName"
                         label="项目标签"
                         width="120"></el-table-column>
        <el-table-column prop="testMemo"
                         label="测试备注"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.testMemo">
              <!-- <div slot="content">
                <div v-for="(name, index) of scope.row.testMemo.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.testMemo}}</span> -->
            </el-tooltip>
            <div v-html='scope.row.testMemo'></div>
          </template>
        </el-table-column>
        <el-table-column prop="sampleRequirements"
                         label="样品要求"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.sampleRequirements">
              <div slot="content">
                <div v-for="(name, index) of scope.row.sampleRequirements.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.sampleRequirements}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="testDays"
                         label="TAT"
                         width="50"></el-table-column>
        <!--<el-table-column label="用户可选"
                         width="150">
          <template slot-scope="scope">
            {{scope.row.isOptional ? '可选' : '不可选'}}
          </template>
        </el-table-column>-->
        <el-table-column label="执行实验室"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.labName">
              <div slot="content">
                <div v-for="(labItemm,labIndex) of scope.row.labName.split(',')"
                     :key="labIndex">{{labItemm}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.labName}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="price"
                         label="价格"
                         width="100">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.price + '/' + scope.row.unit">
              <span class="show_tooltip">{{ scope.row.price }}{{`/${scope.row.unit}`}}</span>
              <!-- <span class="show_tooltip" v-if="scope.row.unit"></span> -->
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="cnasLab"
                         label="CNAS资质"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.cnasLab">
              <div slot="content">
                <div v-for="(name, index) of scope.row.cnasLab.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.cnasLab}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="cmaLab"
                         label="CMA资质"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.cmaLab">
              <div slot="content">
                <div v-for="(name, index) of scope.row.cmaLab.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.cmaLab}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="memo"
                         label="其他备注(内部可见)"
                         width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.memo">
              <div slot="content">
                <div v-for="(name, index) of scope.row.memo.split('\n')"
                     :key="index">{{name}}</div>
              </div>
              <span class="show_tooltip">{{scope.row.memo}}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="personCode"
                         label="最后更新人"
                         width="120"></el-table-column>
        <el-table-column prop="stateDate"
                         label="更新时间"
                         width="140"></el-table-column>
        <el-table-column label="操作"
                         width="170px"
                         fixed="right">
          <template slot-scope="scope">
            <el-button-group>
              <el-button type="primary"
                         size="small"
                         icon="el-icon-edit"
                         @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="small"
                         icon="el-icon-copy-document"
                         @click="handleCopy(scope.row.itemId, scope.row.itemName)">复制</el-button>
              <!-- <el-button size="small" icon="el-icon-delete" @click="handleDel(scope.row.itemId)">删除</el-button> -->
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background
                       layout="sizes, prev, pager, next"
                       :page-sizes="[10, 50, 100]"
                       :total="paginationTotal"
                       @size-change="handleSizeChange"
                       @current-change="handlePageChange"></el-pagination>
      </div>
    </el-col>
    <el-dialog title="测试项目修改/新增"
               :visible="dialog.isVisible"
               width="800px"
               @close="handleClose">
      <el-form ref="dialog"
               :model="dialog"
               label-width="150px"
               :rules="rules">
        <el-form-item label="名称"
                      prop="itemName">
          <!-- <el-input placeholder="请输入名称..."
                    type="text"
                    v-model="dialog.itemName"
                    maxlength="200"></el-input> -->
          <Tinymce :content="dialog.itemName"
                   id='itemName'
                   toolbar=' '
                   plugins=' '
                   @input="emitInput"
                   v-if='isShowEditer'
                   :height='200' />
        </el-form-item>
        <el-form-item label="别名">
          <!-- <el-input placeholder="请输入别名..."
                    type="text"
                    v-model="dialog.itemAlias"
                    maxlength="200"></el-input> -->
          <Tinymce :content="dialog.itemAlias"
                   id='itemAlias'
                   toolbar=' '
                   plugins=' '
                   @input="emitInput"
                   v-if='isShowEditer'
                   :height='200' />
        </el-form-item>
        <el-form-item label="标准号">
          <el-input placeholder="请输入标准号..."
                    type="textarea"
                    v-model="dialog.standardCode"
                    maxlength="200"></el-input>
        </el-form-item>
        <el-form-item label="bu"
                      prop="bu">
          <el-select placeholder="请选择bu..."
                     v-model="dialog.bu"
                     @change="changeBu">
            <el-option v-for="(item, index) of buList"
                       :key="index"
                       :label="item.buName"
                       :value="item.bu"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务线"
                      prop="businessLine">
          <el-select placeholder="请选择业务线..."
                     v-model="dialog.businessLine"
                     @change="handleDialogBusinessLineChange"
                     style="width: 100%">
            <el-option v-for="(item, index) of buinessList"
                       :key="index"
                       :label="item.configName"
                       :value="item.configName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实验室"
                      prop="labItem">
          <el-select multiple
                     placeholder="请选择实验室..."
                     v-model="dialog.labItem"
                     @change="chanegLab"
                     style="width: 100%">
            <el-option v-for="(item, index) of laboratoryList"
                       :key="index"
                       :label="item.labName"
                       :value="item.labName"></el-option>
          </el-select>
        </el-form-item>
        <!-- 弹窗中的样品分类保持单选 -->
        <el-form-item label="样品分类">
          <el-select
            clearable
            placeholder="请先选择业务线..."
            filterable
            multiple
            v-model="dialog.sampleCategoryCode"
            @change="handleDialogSampleCategoryChange"
            :disabled="!dialog.businessLine"
            style="width: 100%">
            <el-option v-for="(item, index) of dialogSampleCategoryList"
                       :key="index"
                       :label="item.categoryName"
                       :value="item.categoryCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样品标准">
          <el-select clearable
                     placeholder="请先选择业务线..."
                     filterable
                     v-model="dialog.sampleStandardCode"
                     :disabled="!dialog.businessLine"
                     style="width: 100%">
            <el-option v-for="(item, index) of dialogSampleStandardList"
                       :key="index"
                       :label="item.standardName"
                       :value="item.standardCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测试周期">
          <!-- <el-input placeholder="请输入测试周期..." type="number" v-model="dialog.testDays" :max="999"></el-input> -->
          <el-input-number v-model="dialog.testDays"
                           :min="0"
                           :max="999"
                           label="请输入测试周期..."></el-input-number>
        </el-form-item>
        <el-form-item label="单价"
                      prop="price"
                      maxlength="8">
          <!-- <el-input placeholder="请输入单价..." type="number" v-model="dialog.price"></el-input> -->
          <el-input-number v-model="dialog.price"
                           :min="0"
                           :max="99999999.99"
                           label="请输入单价..."></el-input-number>
        </el-form-item>
        <el-form-item label="单位">
          <el-input placeholder="请输入单位..."
                    type="text"
                    v-model="dialog.unit"
                    maxlength="20"></el-input>
        </el-form-item>
        <!--<el-form-item label="用户可选">
          <el-radio-group v-model="dialog.isOptional">
            <el-radio value="0"
                      label="0">不可选</el-radio>
            <el-radio value="1"
                      label="1">可选</el-radio>
          </el-radio-group>
        </el-form-item>-->
        <el-form-item label="项目标签">
          <el-input placeholder="请输入项目标签..."
                    type="text"
                    v-model="dialog.labelName"
                    maxlength="8"></el-input>
        </el-form-item>
        <el-form-item label="样品要求">
          <el-input placeholder="请输入样品要求..."
                    type="textarea"
                    v-model="dialog.sampleRequirements"
                    maxlength="1000"></el-input>
        </el-form-item>
        <el-form-item label="测试备注">
          <!-- <el-input placeholder="请输入测试备注..."
                    type="textarea"
                    v-model="dialog.testMemo"
                    maxlength="1000"></el-input> -->
          <Tinymce :content="dialog.testMemo"
                   id='testMemo'
                   toolbar=' '
                   plugins=' '
                   @input="emitInput"
                   v-if='isShowEditer'
                   :height='200' />
        </el-form-item>
        <el-form-item label="CNAS资质">
          <el-input placeholder="请输入CNAS资质..."
                    type="textarea"
                    v-model="dialog.cnasLab"
                    maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="CMA资质">
          <el-input placeholder="请输入CMA资质..."
                    type="textarea"
                    v-model="dialog.cmaLab"
                    maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="testItemId">
          <el-input placeholder="请输入testItemId..."
                    type="text"
                    v-model="dialog.testLineId"
                    maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="其他备注(内部可见)">
          <el-input placeholder="请输入其他备注..."
                    type="textarea"
                    v-model="dialog.memo"
                    maxlength="5000"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary"
                   @click="submitForm('dialog')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="批量修改测试项目"
               :visible="modifyDialog.isVisible"
               width="80%"
               @close="handleCloseModify">
      <el-form ref="modifyDialog"
               :model="modifyDialog"
               label-width="170px">
        <el-form-item label="请选择需要修改的项目">
          <el-select v-model="modifyDialog.key"
                     @change='handleChangItem'>
            <el-option v-for='(item, index) of modifyItem'
                       :key='index'
                       :label='item.columnName'
                       :value='item.columnCode'></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请输入修改后的值">
          <el-input style="width: 100%;"
                    v-if='modifyDialog.type === 1 && modifyDialog.key === "unit"'
                    placeholder="请输入..."
                    type="text"
                    v-model="modifyDialog.value"
                    maxlength="20"></el-input>
          <el-input style="width: 100%;"
                    v-if='modifyDialog.type === 1 && (modifyDialog.key === "itemName" || modifyDialog.key === "itemAlias" || modifyDialog.key === "standardCode")'
                    placeholder="请输入..."
                    type="text"
                    v-model="modifyDialog.value"
                    maxlength="200"></el-input>
          <el-input style="width: 100%;"
                    v-if='modifyDialog.type === 1 &&  modifyDialog.key === "labelName"'
                    placeholder="请输入..."
                    type="text"
                    v-model="modifyDialog.value"
                    maxlength="8"></el-input>
          <el-input style="width: 100%;"
                    v-if='modifyDialog.type === 2 &&  (modifyDialog.key === "cmaLab" || modifyDialog.key === "cnasLab")'
                    placeholder="请输入..."
                    type="textarea"
                    v-model="modifyDialog.value"
                    maxlength="500"></el-input>
          <el-input style="width: 100%;"
                    v-if='modifyDialog.type === 2 && (modifyDialog.key === "sampleRequirements" || modifyDialog.key === "testMemo")'
                    placeholder="请输入..."
                    type="textarea"
                    v-model="modifyDialog.value"
                    maxlength="1000"></el-input>
          <el-input style="width: 100%;"
                    v-if='modifyDialog.type === 2 &&  modifyDialog.key === "memo"'
                    placeholder="请输入..."
                    type="textarea"
                    v-model="modifyDialog.value"
                    maxlength="5000"></el-input>
          <el-input-number style="width: 100%;"
                           v-if='modifyDialog.type === 5 && modifyDialog.key === "price"'
                           v-model="modifyDialog.value"
                           :min="0"
                           :max="99999999.99"
                           label="请输入..."></el-input-number>
          <el-input-number style="width: 100%;"
                           v-if='modifyDialog.type === 5 && modifyDialog.key === "testDays"'
                           v-model="modifyDialog.value"
                           :min="0"
                           :max="999"
                           label="请输入..."></el-input-number>
          <el-select style="width: 100%;"
                     v-if='modifyDialog.type === 3 && modifyDialog.key === "businessLine"'
                     v-model="modifyDialog.value"
                     placeholder="请选择...">
            <el-option v-for='(item ,index) of changeEnum'
                       :key='index'
                       :value='item.configName'
                       :label='item.configName'></el-option>
          </el-select>
          <el-select style="width: 100%;"
                     v-if='modifyDialog.type === 3 && modifyDialog.key === "isOptional"'
                     v-model="modifyDialog.value"
                     placeholder="请选择...">
            <el-option label="不可选"
                       value="0"></el-option>
            <el-option label="可选"
                       value="1"></el-option>
          </el-select>
          <el-select style="width: 100%;"
                     v-if='modifyDialog.type === 4'
                     @change="chanegLab"
                     v-model="modifyDialog.value"
                     multiple　
                     placeholder="请选择...">
            <el-option v-for='(item ,index) of changeEnum'
                       :key='index'
                       :value='item.labId'
                       :label='item.labName'></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="已选的数据">
          <el-table :data='modifyDialogTable'
                    stripe
                    border
                    style="width: 100%"
                    size="small">
            <el-table-column prop="itemId"
                             label="ID"
                             width="70"></el-table-column>
            <el-table-column prop="businessLine"
                             label="业务线"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.businessLine">
                  <span class="show_tooltip">{{scope.row.businessLine}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="itemName"
                             label="名称"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.itemName">
                  <span class="show_tooltip">{{scope.row.itemName}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="itemAlias"
                             label="别名"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.itemAlias">
                  <span class="show_tooltip">{{scope.row.itemAlias}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="standardCode"
                             label="标准"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.standardCode">
                  <div slot="content">
                    <div v-for="(name, index) of scope.row.standardCode.split('\n')"
                         :key="index">{{name}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.standardCode}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="labelName"
                             label="项目标签"
                             width="120"></el-table-column>
            <el-table-column prop="testMemo"
                             label="测试备注"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.testMemo">
                  <div slot="content">
                    <div v-for="(name, index) of scope.row.testMemo.split('\n')"
                         :key="index">{{name}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.testMemo}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="sampleRequirements"
                             label="样品要求"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.sampleRequirements">
                  <div slot="content">
                    <div v-for="(name, index) of scope.row.sampleRequirements.split('\n')"
                         :key="index">{{name}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.sampleRequirements}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <!--<el-table-column label="用户可选"
                             width="150">
              <template slot-scope="scope">
                {{scope.row.isOptional ? '可选' : '不可选'}}
              </template>
            </el-table-column>-->
            <el-table-column prop="testDays"
                             label="TAT"
                             width="50"></el-table-column>
            <el-table-column label="执行实验室"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.labName">
                  <div slot="content">
                    <div v-for="(labItemm,labIndex) of scope.row.labName.split(',')"
                         :key="labIndex">{{labItemm}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.labName}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="price"
                             label="价格"
                             width="100">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.price + '/' + scope.row.unit">
                  <span class="show_tooltip">{{ scope.row.price }}{{`/${scope.row.unit}`}}</span>
                  <!-- <span class="show_tooltip" v-if="scope.row.unit"></span> -->
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="cnasLab"
                             label="CNAS资质"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.cnasLab">
                  <div slot="content">
                    <div v-for="(name, index) of scope.row.cnasLab.split('\n')"
                         :key="index">{{name}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.cnasLab}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="cmaLab"
                             label="CMA资质"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.cmaLab">
                  <div slot="content">
                    <div v-for="(name, index) of scope.row.cmaLab.split('\n')"
                         :key="index">{{name}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.cmaLab}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="memo"
                             label="其他备注(内部可见)"
                             width="150">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.memo">
                  <div slot="content">
                    <div v-for="(name, index) of scope.row.memo.split('\n')"
                         :key="index">{{name}}</div>
                  </div>
                  <span class="show_tooltip">{{scope.row.memo}}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="personCode"
                             label="最后更新人"
                             width="120"></el-table-column>
            <el-table-column prop="stateDate"
                             label="更新时间"
                             width="140"></el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="handleCloseModify">取 消</el-button>
        <el-button type="primary"
                   @click="submitModifyForm('modifyDialog')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="复制测试项目"
               :visible="copyDialog.isVisible"
               width="400px"
               @close="handleCloseCopy">
      <template>
        <el-radio-group v-model="isTrue">
          <el-radio :label="0">仅复制项目</el-radio>
          <el-radio :label="1">复制项目且复制关联逻辑</el-radio>
        </el-radio-group>
      </template>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="handleCloseCopy">取 消</el-button>
        <el-button type="primary"
                   @click="submitCopy">确 定</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import { ajaxNew } from './../../api/index'
import { apiHost, currDate } from './../../assets/javascript/util'
import { exportTestItem } from './../../api/Files'
import Tinymce from './../../components/Tinymce.vue'
import { qryBusiness, qryLaboratory, itemCopy, qrySampleCategory, qrySampleCategoryByLine, qrySampleStandard, qrySampleStandardByLine } from './../../api/common'

@Component({
  name: 'TestItem',
  components: {
    Tinymce,
  },
})
class TestItem extends Vue {
  @Prop() private isQuestionList
  @Mutation('setTestItemRefresh') setTestItemRefresh

  private rules = {
    itemName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
    bu: [
      {
        required: true,
        message: '请至少选择一个bu',
        trigger: 'change',
      },
    ],
    businessLine: [
      {
        required: true,
        message: '请至少选择一个业务线',
        trigger: 'change',
      },
    ],
    // labItem: [
    //   {
    //     required: true,
    //     message: "请至少选择一个实验室",
    //     trigger: "change"
    //   }
    // ]
  }
  private dialog = {
    bu: '',
    businessLine: '',
    businessLineId: '',
    itemAlias: '',
    itemId: 0,
    itemName: '',
    labItem: [],
    sampleCategoryCode: '',
    sampleStandardCode: '',
    cnasLab: '',
    cmaLab: '',
    memo: '',
    price: '',
    sampleRequirements: '',
    standardCode: '',
    testDays: '',
    testMemo: '',
    unit: '',
    labName: '',
    isVisible: false,
    labNames: [],
    isOptional: '0',
    labelName: '',
    testLineId: '',
    sampleCategoryCode: [],
  }
  private itemList = []
  private qryItemParam = {
    labNames: [],
    labName: '',
    itemName: '',
    itemAlias: '',
    // businessLine: '',
    businessLineId: '',
    pageNum: 1,
    pageRow: 10,
    stateDateArray: [],
    stateDate: '',
    sampleCategoryCode: '',
    sampleStandardCode: '',
    sampleCategoryCode: [],
  }
  private paginationTotal = 0
  private buList = []
  private buinessList = []
  private businessLineAll = []
  private laboratoryList = []
  private labItem = []
  // 多选删除
  private sysItem = []
  // 上传文件
  private uploadFile = {
    showFileList: false,
    host: apiHost,
  }
  // 全部实验室
  private laboratoryListAll = []
  private questionId = ''
  private subjectNo = ''
  private optionNo = ''
  private modifyDialog = {
    isVisible: false,
    value: '',
    key: '',
    type: 0, // 显示方式1:文本,2:多行文本,3:下拉,4:下拉多选,5:数字文本
  }
  private modifyDialogTable = []
  private modifyItem = []
  private changeEnum = []
  private system = 'ticCenter'
  private isShowEditer = false
  private isTrue = 0
  private copyDialog = {
    isVisible: false,
    itemId: 0,
    itemName: ''
  }
  private sampleCategoryList = []
  private sampleStandardList = []
  private dialogSampleCategoryList = []
  private dialogSampleStandardList = []

  // 切换分页
  handlePageChange(page: number): void {
    this.qryItemParam.pageNum = page
    this.qryItemList()
  }

  // 切换每页条数
  handleSizeChange(size: number): void {
    this.qryItemParam.pageRow = size
    this.qryItemList()
  }

  // 选择bu
  changeBu(val): void {
    this.buinessList = []
    this.dialog.businessLine = ''
    this.laboratoryList = []
    this.dialog.labItem = []
    this.getBusinessList(val)
    this.getLaboratoryList(val, 'laboratoryList')
  }

  // 选择实验室
  chanegLab(val): void {
    this.labItem = []
    this.laboratoryList.forEach((lab) => {
      val.forEach((v) => {
        if (lab.labName === v) {
          this.labItem.push({
            labId: lab.labId,
            labName: lab.labName,
          })
        }
      })
    })
    // 格式化批量修改实验室选择
    if (this.changeEnum.length) {
      this.changeEnum.forEach((lab) => {
        val.forEach((v) => {
          if (lab.labId === v) {
            this.labItem.push({
              labId: lab.labId,
              labName: lab.labName,
            })
          }
        })
      })
    }
  }
  chanegLabAll(val): void {
    this.qryItemParam.labName = val.join(',')
  }

  // 提交校验
  submitForm(formName) {
    const ref: any = this.$refs[formName]
    ref.validate((valid) => {
      if (valid) {
        if (this.dialog.itemId) {
          this.modItem()
        } else {
          this.addItem()
        }
      } else {
        this['$message'].error('请完成必填项')
      }
    })
  }

  // 搜索点击
  handleSearch(): void {
    this.qryItemParam.itemAlias = this.qryItemParam.itemName
    this.qryItemParam.pageNum = 1
    this.qryItemList()
  }
  handleAdd() {
    this.dialog.title = '新增'
    this.dialog.bu = ''
    this.dialog.businessLine = ''
    this.dialog.itemAlias = ''
    this.dialog.itemId = 0
    this.dialog.itemName = ''
    this.dialog.labItem = []
    this.dialog.memo = ''
    this.dialog.cnasLab = ''
    this.dialog.cmaLab = ''
    this.dialog.price = ''
    this.dialog.sampleRequirements = ''
    this.dialog.standardCode = ''
    this.dialog.testDays = ''
    this.dialog.testMemo = ''
    this.dialog.unit = ''
    this.dialog.labName = ''
    this.dialog.isVisible = true
    this.dialog.labNames = []
    this.dialog.isOptional = '0'
    this.dialog.labelName = ''
    this.buinessList = []
    this.laboratoryList = []
    this.dialog.businessLineId = ''
    this.dialog.sampleCategoryCode = []
    this.dialog.sampleStandardCode = ''
    this.dialog.testLineId = ''
    this.dialogSampleCategoryList = []
    this.dialogSampleStandardList = []
    this.isShowEditer = true
  }
  handleEdit(row): void {
    const {
      bu,
      businessLine,
      itemAlias,
      itemId,
      itemName,
      labNames,
      cnasLab,
      cmaLab,
      memo,
      price,
      sampleRequirements,
      standardCode,
      testDays,
      testMemo,
      unit,
      labelName,
      isOptional,
      businessLineId,
      sampleCategoryCode,
      sampleCategories,
      sampleStandardCode,
      testLineId
    } = row

    this.dialog.title = '编辑'
    this.getBusinessList(bu)
    this.getLaboratoryList(bu, 'laboratoryList')
    this.dialog.bu = bu
    this.dialog.businessLine = businessLine
    this.dialog.businessLineId = businessLineId || ''
    this.dialog.itemAlias = itemAlias
    this.dialog.itemId = itemId
    this.dialog.itemName = itemName
    this.dialog.labItem = labNames || []
    this.dialog.cnasLab = cnasLab
    this.dialog.cmaLab = cmaLab
    this.dialog.memo = memo
    this.dialog.price = price
    this.dialog.sampleRequirements = sampleRequirements
    this.dialog.standardCode = standardCode
    this.dialog.testDays = testDays
    this.dialog.testMemo = testMemo
    this.dialog.unit = unit
    this.dialog.labelName = labelName
    this.dialog.isOptional = String(isOptional)
    this.dialog.testLineId = testLineId || ''
    this.dialog.isVisible = true
    this.isShowEditer = true

    // 修复问题2：处理样品分类数据，使用正确的 sampleCategories 字段
    if (sampleCategories && Array.isArray(sampleCategories)) {
      this.dialog.sampleCategoryCode = [...sampleCategories];
    } else if (sampleCategories && typeof sampleCategories === 'string') {
      // 如果是字符串，尝试解析
      try {
        const parsed = JSON.parse(sampleCategories);
        this.dialog.sampleCategoryCode = Array.isArray(parsed) ? parsed : [sampleCategories];
      } catch (e) {
        this.dialog.sampleCategoryCode = [sampleCategories];
      }
    } else if (sampleCategoryCode) {
      // 备用：如果 sampleCategories 不存在，才使用 sampleCategoryCode
      this.dialog.sampleCategoryCode = Array.isArray(sampleCategoryCode) ? sampleCategoryCode : [sampleCategoryCode];
    } else {
      this.dialog.sampleCategoryCode = [];
    }

    this.dialog.sampleStandardCode = sampleStandardCode || '';

    // 如果没有businessLineId但有businessLine，则根据businessLine查找对应的ID
    if (!this.dialog.businessLineId && businessLine) {
      const selectedBusinessLine = this.businessLineAll.find(item => item.configName === businessLine)
      if (selectedBusinessLine) {
        const lineId = selectedBusinessLine.lineId ||
                       selectedBusinessLine.configValue ||
                       selectedBusinessLine.id ||
                       selectedBusinessLine.value ||
                       selectedBusinessLine.configId
        this.dialog.businessLineId = lineId || ''
      }
    }

    // 根据businessLineId加载样品分类和样品标准
    if (this.dialog.businessLineId) {
      // 加载样品分类列表
      this.getDialogSampleCategoryList(this.dialog.businessLineId)

      // 修复问题1：无论 sampleStandardCode 是否为空，都要根据业务线获取样品标准列表
      this.getDialogSampleStandardListByLine(this.dialog.businessLineId);

      // 如果有样品分类，也可以根据分类获取标准（作为补充）
      if (this.dialog.sampleCategoryCode.length === 1) {
        setTimeout(() => {
          this.getDialogSampleStandardList(this.dialog.sampleCategoryCode[0], this.dialog.businessLineId)
        }, 200) // 增加延时确保分类列表已加载
      }
    }

    console.log('Edit dialog data:', this.dialog)
    console.log('Original sampleCategories:', sampleCategories)
    console.log('Original sampleCategoryCode:', sampleCategoryCode)
  }
  handleCopy(itemId, itemName): void {
    this.copyDialog.itemId = itemId
    this.copyDialog.itemName = itemName
    this.copyDialog.isVisible = true
  }
  handleDel() {
    this['$confirm'](`您确认删除？`)
      .then(() => {
        if (!this['$route'].query.groupNo) {
          this.delItem()
        } else {
          this.delItemGroup()
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 表格单项选择
  tableSelect(val): void {
    this.sysItem = val.map((v) => {
      return { itemId: v.itemId }
    })
    this.modifyDialogTable = val
  }

  // 表格全部选择
  tableSelectAll(val): void {
    this.sysItem = val.map((v) => {
      return { itemId: v.itemId }
    })
    this.modifyDialogTable = val
  }

  // 文件上传
  onSuccess(res) {
    if (res.resultCode === '0') {
      this.fileImport(res.data.fileName)
    } else {
      this['$message'].error(res.resultMsg)
    }
  }

  // 获取bu列表
  getBuList(): void {
    ajaxNew('/business/api.v1.center/bu/qryBySystem', {}, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          // this.buList = res.data.items.filter((v) => {
          //   return v.parentCode !== "0";
          // });
          this.buList = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 获取业务线列表
  getBusinessList(bu: string): void {
    qryBusiness({
      bu,
      configType: 100000,
      system: this.system,
    }).then((res) => {
      Object.values(res).forEach((v) => {
        this.buinessList = this.buinessList.concat(v)
      })
    })
  }

  // 获取全业务线
  getBusinessListAll(source?): void {
    qryBusiness({ configType: 100000, system: this.system }).then((res) => {
      if (!source) {
        this.businessLineAll = res
      } else {
        this[source] = res
      }
    })
  }

  // 获取样品分类列表
  getSampleCategoryList(lineId?: number): void {
    const params = lineId ? { lineId } : {}
    console.log('Getting sample categories with params:', params)
    qrySampleCategoryByLine(params).then((res) => {
      console.log('Sample categories response:', res)

      // 在前端添加"全部"选项
      const allOption = {
        categoryId: 0,
        categoryName: "全部",
        categoryCode: "ALL",
        categoryPath: "全部",
        bu: "",
        lineId: lineId || 0,
        parentCategoryId: 0,
        parentCategoryCode: "",
        memo: "",
        externalId: "",
        state: 1,
        standards: ""
      };

      this.sampleCategoryList = [allOption, ...(res || [])];
      console.log('Sample categories list updated:', this.sampleCategoryList)
    }).catch((error) => {
      console.error('Error getting sample categories:', error)
      this.sampleCategoryList = []
    })
  }

  // 获取样品标准列表
  getSampleStandardList(categoryCode: string, lineId?: number): void {
    const params: any = {
      categoryCode: categoryCode
    }

    // 如果有lineId，也传递给接口
    if (lineId) {
      params.lineId = lineId
    }

    console.log('Getting sample standards with params:', params)
    qrySampleStandard(params).then((res) => {
      console.log('Sample standards response:', res)
      this.sampleStandardList = res || []
    }).catch((error) => {
      console.error('Error getting sample standards:', error)
      this.sampleStandardList = []
    })
  }

  // 弹窗专用 - 获取样品分类列表
  getDialogSampleCategoryList(lineId: number): void {
    if (!lineId) {
      console.warn('lineId is required for getting sample categories');
      this.dialogSampleCategoryList = [];
      return;
    }

    const params = { lineId };
    console.log('Getting dialog sample categories with params:', params);

    qrySampleCategoryByLine(params).then((res) => {
      console.log('Dialog sample categories response:', res);

      // 在前端添加"全部"选项
      const allOption = {
        categoryId: 0,
        categoryCode: 'ALL',
        categoryName: '全部'
      };

      this.dialogSampleCategoryList = [allOption, ...(res || [])];
    }).catch((error) => {
      console.error('Error getting dialog sample categories:', error);
      this.dialogSampleCategoryList = [];
    });
  }

  // 弹窗专用 - 获取样品标准列表
  getDialogSampleStandardList(categoryCode: string, lineId: number): void {
    if (!categoryCode || !lineId) {
      console.warn('Both categoryCode and lineId are required for getting sample standards')
      this.dialogSampleStandardList = []
      return
    }

    const params = {
      categoryCode: categoryCode,
      lineId: lineId
    }

    console.log('Getting dialog sample standards with params:', params)
    qrySampleStandard(params).then((res) => {
      console.log('Dialog sample standards response:', res)
      this.dialogSampleStandardList = res || []
    }).catch((error) => {
      console.error('Error getting dialog sample standards:', error)
      this.dialogSampleStandardList = []
    })
  }

  // 弹窗专用 - 样品分类变化处理 - 支持多选和ALL互斥
  handleDialogSampleCategoryChange(selectedCategories: string[]): void {
    // 确保selectedCategories是数组
    if (!Array.isArray(selectedCategories)) {
      selectedCategories = selectedCategories ? [selectedCategories] : [];
    }

    // 更新dialog中的值
    this.dialog.sampleCategoryCode = [...selectedCategories];

    // 如果选择了ALL
    if (selectedCategories.includes('ALL')) {
      // 如果之前没有选择ALL，现在选择了ALL，则清空其他选项，只保留ALL
      if (this.dialog.sampleCategoryCode.length > 1 ||
          (this.dialog.sampleCategoryCode.length === 1 && !this.dialog.sampleCategoryCode.includes('ALL'))) {
        this.dialog.sampleCategoryCode = ['ALL'];
      }
    } else {
      // 如果没有选择ALL，但之前选择了ALL，则移除ALL
      if (this.dialog.sampleCategoryCode.includes('ALL')) {
        this.dialog.sampleCategoryCode = selectedCategories.filter(code => code !== 'ALL');
      }
    }
  }

  // 弹窗专用 - 业务线变化处理
  handleDialogBusinessLineChange(businessLine: string): void {
    // 清空样品分类和标准
    this.dialog.sampleCategoryCode = [];
    this.dialog.sampleStandardCode = '';
    this.dialogSampleCategoryList = [];
    this.dialogSampleStandardList = [];

    // 根据业务线获取对应的lineId并设置businessLineId
    if (businessLine) {
      const selectedBusinessLine = this.businessLineAll.find(item => item.configName === businessLine);
      console.log('Dialog selected business line:', selectedBusinessLine);

      let lineId = null;
      if (selectedBusinessLine) {
        lineId = selectedBusinessLine.lineId ||
                 selectedBusinessLine.configValue ||
                 selectedBusinessLine.id ||
                 selectedBusinessLine.value ||
                 selectedBusinessLine.configId;
      }

      console.log('Dialog Line ID:', lineId);

      // 设置businessLineId用于保存
      this.dialog.businessLineId = lineId || '';

      // 根据lineId获取样品分类
      if (lineId) {
        this.getDialogSampleCategoryList(lineId);

        // 根据业务线直接获取样品标准（不需要categoryCode参数）
        this.getDialogSampleStandardListByLine(lineId);
      }
    } else {
      // 如果没有选择业务线，清空businessLineId
      this.dialog.businessLineId = '';
    }
  }

  // 业务线变化处理
  handleBusinessLineChange(businessLine: string): void {
    // 清空样品分类和标准
    this.qryItemParam.sampleCategoryCode = [];
    this.qryItemParam.sampleStandardCode = '';
    this.sampleCategoryList = [];
    this.sampleStandardList = [];

    // 根据业务线获取对应的lineId
    if (businessLine) {
      const selectedBusinessLine = this.businessLineAll.find(item => item.configName === businessLine);
      console.log('Selected business line:', selectedBusinessLine);

      let lineId = null;
      if (selectedBusinessLine) {
        lineId = selectedBusinessLine.lineId ||
                 selectedBusinessLine.configValue ||
                 selectedBusinessLine.id ||
                 selectedBusinessLine.value ||
                 selectedBusinessLine.configId;
      }

      console.log('Line ID:', lineId);

      // 设置businessLineId用于接口查询
      this.qryItemParam.businessLineId = lineId || '';

      if (lineId) {
        // 根据业务线获取样品分类
        this.getSampleCategoryList(lineId);

        // 根据业务线直接获取样品标准
        this.getSampleStandardListByLine(lineId);
      } else {
        this.getSampleCategoryList();
      }
    } else {
      // 如果没有选择业务线，清空businessLineId
      this.qryItemParam.businessLineId = '';
      this.getSampleCategoryList();
    }
    this.handleSearch();
  }

  // 根据业务线获取样品标准列表
  getSampleStandardListByLine(lineId: number): void {
    if (!lineId) {
      console.warn('lineId is required for getting sample standards by line');
      this.sampleStandardList = [];
      return;
    }

    const params = {
      lineId: lineId
    };

    console.log('Getting sample standards by line with params:', params);
    // 使用正确的API方法：qrySampleStandardByLine 调用 /business/api.v1.center/sampleStandard/qryByLine
    qrySampleStandardByLine(params).then((res) => {
      console.log('Sample standards by line response:', res);
      this.sampleStandardList = res || [];
    }).catch((error) => {
      console.error('Error getting sample standards by line:', error);
      this.sampleStandardList = [];
    });
  }

  // 弹窗专用 - 根据业务线获取样品标准列表
  getDialogSampleStandardListByLine(lineId: number): void {
    if (!lineId) {
      console.warn('lineId is required for getting sample standards');
      this.dialogSampleStandardList = [];
      return;
    }

    const params = { lineId };
    console.log('Getting dialog sample standards by line with params:', params);

    qrySampleStandardByLine(params).then((res) => {
      console.log('Dialog sample standards by line response:', res);
      this.dialogSampleStandardList = res || [];
    }).catch((error) => {
      console.error('Error getting dialog sample standards by line:', error);
      this.dialogSampleStandardList = [];
    });
  }

  // 样品分类变化处理 - 支持多选和ALL互斥
  handleSampleCategoryChange(selectedCategories: string[]): void {
    // 确保selectedCategories是数组
    if (!Array.isArray(selectedCategories)) {
      selectedCategories = selectedCategories ? [selectedCategories] : [];
    }

    // 更新查询参数中的值
    this.qryItemParam.sampleCategoryCode = [...selectedCategories];

    // 如果选择了ALL
    if (selectedCategories.includes('ALL')) {
      // 如果之前没有选择ALL，现在选择了ALL，则清空其他选项，只保留ALL
      if (this.qryItemParam.sampleCategoryCode.length > 1 ||
          (this.qryItemParam.sampleCategoryCode.length === 1 && !this.qryItemParam.sampleCategoryCode.includes('ALL'))) {
        this.qryItemParam.sampleCategoryCode = ['ALL'];
      }
    } else {
      // 如果没有选择ALL，但之前选择了ALL，则移除ALL
      if (this.qryItemParam.sampleCategoryCode.includes('ALL')) {
        this.qryItemParam.sampleCategoryCode = selectedCategories.filter(code => code !== 'ALL');
      }
    }

    // 移除以下代码，不再清空样品标准选择
    // this.qryItemParam.sampleStandardCode = '';
    // this.sampleStandardList = [];

    // 注意：样品标准查询现在改为业务线触发，这里不再查询样品标准
    // this.handleSearch() // 可以根据需要决定是否触发搜索
  }

  // 获取实验室列表
  getLaboratoryList(bu: string, source): void {
    qryLaboratory({ bu, configType: 100000, system: this.system }).then(
      (res) => {
        this[source] = res
      }
    )
  }

  // 获取测试项目
  qryItemList(): void {
    if (
      this.qryItemParam.stateDateArray &&
      this.qryItemParam.stateDateArray.length
    ) {
      this.qryItemParam.stateDate = this.qryItemParam.stateDateArray.join('/')
    } else {
      this.qryItemParam.stateDate = ''
    }

    // 处理查询参数：将sampleCategoryCode数组转换为sampleCategories
    const queryParams = { ...this.qryItemParam };
    queryParams.sampleCategories = this.qryItemParam.sampleCategoryCode || [];
    delete queryParams.sampleCategoryCode;
    delete queryParams.businessLine;

    ajaxNew('/business/api.v1.center/item/qry', queryParams, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          res.data.items.forEach((i) => {
            if (i.labName) i.labNames = i.labName.split(',')
          })

          this.itemList = res.data.items
          console.log(this.itemList)
          this.paginationTotal = res.data.totalNum
        } else {
          this['$message'].error(res.resultMsg)
        }
        this.isShowEditer = false
      })
      .catch((e) => {
        console.log(e)
        this.isShowEditer = false
      })
  }

  // 修改addItem方法
  addItem(): void {
    this.dialog.labItem = this.labItem;

    // 处理样品分类数据：直接使用sampleCategoryCode数组作为sampleCategories
    const submitData = { ...this.dialog };
    submitData.sampleCategories = this.dialog.sampleCategoryCode || [];

    // 移除原来的sampleCategoryCode参数
    delete submitData.sampleCategoryCode;
    delete submitData.labName;
    delete submitData.labNames;
    delete submitData.itemId;

    ajaxNew('/business/api.v1.center/item/add', submitData, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '添加成功',
            onClose: () => {
              this.dialog.isVisible = false;
              this.qryItemList();
            },
          });
        } else {
          this.dialog.isVisible = false;
          this['$message'].error(res.resultMsg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }

  // 修改modItem方法
  modItem(): void {
    this.labItem = [];
    this.laboratoryList.forEach((lab) => {
      this.dialog.labItem.forEach((v) => {
        if (lab.labName === v) {
          this.labItem.push({
            labId: lab.labId,
            labName: lab.labName,
          });
        }
      });
    });
    this.dialog.labItem = this.labItem;

    // 处理样品分类数据：直接使用sampleCategoryCode数组作为sampleCategories
    const submitData = { ...this.dialog };
    submitData.sampleCategories = this.dialog.sampleCategoryCode || [];

    // 移除原来的sampleCategoryCode参数
    delete submitData.sampleCategoryCode;
    delete submitData.labName;
    delete submitData.labNames;

    ajaxNew('/business/api.v1.center/item/mod', submitData, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '修改成功',
            onClose: () => {
              this.dialog.isVisible = false;
              this.setTestItemRefresh(true);
              this.qryItemList();
            },
          });
        } else {
          this.dialog.isVisible = false;
          this['$message'].error(res.resultMsg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }

  // 删除测试项目
  delItem(): void {
    ajaxNew('/business/api.v1.center/item/del', this.sysItem, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '删除成功',
            onClose: () => {
              this.qryItemList()
              this.setTestItemRefresh(true)
            },
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 修改测试项目
  modItem(): void {
    this.labItem = [];
    this.laboratoryList.forEach((lab) => {
      this.dialog.labItem.forEach((v) => {
        if (lab.labName === v) {
          this.labItem.push({
            labId: lab.labId,
            labName: lab.labName,
          });
        }
      });
    });
    this.dialog.labItem = this.labItem;

    // 处理样品分类数据：将sampleCategoryCode数组转换为sampleCategories，并移除sampleCategoryCode
    const submitData = { ...this.dialog };
    submitData.sampleCategories = this.dialog.sampleCategoryCode || [];
    delete submitData.sampleCategoryCode;
    delete submitData.labName;
    delete submitData.labNames;

    ajaxNew('/business/api.v1.center/item/mod', submitData, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '修改成功',
            onClose: () => {
              this.dialog.isVisible = false;
              this.setTestItemRefresh(true);
              this.qryItemList();
            },
          });
        } else {
          this.dialog.isVisible = false;
          this['$message'].error(res.resultMsg);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }

  // 关联测试项目
  relateItem(): void {
    if (!this['$route'].query.partNo) {
      if (this.sysItem.length) {
        ajaxNew(
          '/business/api.v1.question/qitem/relate',
          {
            questionId: this.questionId,
            subjectNo: this.subjectNo,
            optionNo: this.optionNo,
            sysItem: this.sysItem,
          },
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this['$message']({
                type: 'success',
                message: '关联成功',
                onClose: () => {
                  this.setTestItemRefresh(true)
                },
              })
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            console.log(e)
          })
      }
    } else {
      this.relateItemGroup()
    }
  }

  // 关联测试项目组
  relateItemGroup(): void {
    console.log(this.sysItem)
    if (this.sysItem.length) {
      ajaxNew(
        '/business/api.v1.question/qitemrelate/add',
        {
          sysItem: this.sysItem,
          questionId: this['$route'].query.questionId,
          groupNo: this['$route'].query.groupNo,
        },
        'post'
      )
        .then((res) => {
          if (res.resultCode === '0') {
            this['$message']({
              type: 'success',
              message: '关联成功',
              onClose: () => {
                this.setTestItemRefresh(true)
              },
            })
          } else {
            this['$message'].error(res.resultMsg)
          }
        })
        .catch((e) => {
          console.log(e)
        })
    }
  }
  // 删除测试项目组
  delItemGroup(): void {
    ajaxNew(
      '/business/api.v1.question/qitemrelate/del',
      {
        sysItem: this.sysItem,
        groupNo: this['$route'].query.groupNo,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message']({
            type: 'success',
            message: '删除成功',
            onClose: () => {
              this.qryItemList()
              this.setTestItemRefresh(true)
            },
          })
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 关闭弹窗
  handleClose(): void {
    this.dialog.isVisible = false
    this.isShowEditer = false
  }

  // 文件导入
  fileImport(fileName) {
    ajaxNew(
      '/business/api.v1.center/item/imp',
      { fileName, system: this.system },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message'].success('模板导入成功')
          this.qryItemList()
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  selectBlur(e) {
    if (e.target.value) {
      this.qryItemParam.businessLine = e.target.value
    }
  }

  // 数据导出
  handleExport(): void {
    // 处理导出参数：移除businessLine参数，将sampleCategoryCode数组转换为sampleCategories
    const exportParams = { ...this.qryItemParam };
    exportParams.sampleCategories = this.qryItemParam.sampleCategoryCode || [];
    delete exportParams.sampleCategoryCode; // 移除原参数
    delete exportParams.businessLine; // 移除businessLine参数，不发送给服务端

    exportTestItem(exportParams, `QMS测试项目导入模板.xlsx`);
  }

  // 批量修改显示
  handlemMltipleModify(): void {
    this.getModifyItem()
    this.labItem = []
    this.modifyDialog = {
      isVisible: true,
      value: '',
      key: '',
      type: 0, // 显示方式1:文本,2:多行文本,3:下拉,4:下拉多选,5:数字文本
    }
  }
  // 关闭批量修改
  handleCloseModify(): void {
    this.modifyDialogTable = []
    this.modifyItem = []
    this.changeEnum = []
    this.qryItemList()
    this.modifyDialog.isVisible = false
  }

  handleCloseCopy(): void {
    this.copyDialog.isVisible = false
    this.isTrue = 0
  }

  // 选项项目
  handleChangItem(val): void {
    this.modifyDialog.value = ''
    const currItem = this.modifyItem.filter((v) => val === v.columnCode)[0]
    this.modifyDialog.type = currItem.showType
    // 下拉框选择获取枚举值
    if (this.modifyDialog.type === 3 || this.modifyDialog.type === 4) {
      if (currItem.columnCode === 'businessLine') {
        this.getBusinessListAll('changeEnum')
      }
      if (currItem.columnCode === 'labItem') {
        this.getLaboratoryList('', 'changeEnum')
      }
    }
  }

  // 获取选项项目
  getModifyItem(): void {
    ajaxNew(
      '/business/api.v1.center/column/qry',
      {
        tableCode: 'SYS_ITEM',
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          res.data.items.sort((a, b) => {
            return a.sortShow - b.sortShow
          })
          this.modifyItem = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 提交修改
  submitModifyForm(formName) {
    const params = {
      itemStr: '',
      modName: this.modifyDialog.key,
    }
    const itemArr = []
    if (this.modifyDialogTable.length) {
      this.modifyDialogTable.forEach((v) => {
        itemArr.push(v.itemId)
      })
    }
    params.itemStr = itemArr.join(',')
    params[this.modifyDialog.key] = this.modifyDialog.value
    if (this.modifyDialog.key === 'labItem')
      params[this.modifyDialog.key] = this.labItem
    if (!this.modifyDialog.key) {
      this['$message'].error('请选择修改的项目')
    } else if (
      !this.modifyDialog.value &&
      (this.modifyDialog.key === 'itemName' ||
        this.modifyDialog.key === 'businessLine' ||
        this.modifyDialog.key === 'testDays' ||
        this.modifyDialog.key === 'price')
    ) {
      this['$message'].error('请输入修改后的值')
    } else if (!params.itemStr) {
      this['$message'].error('请选择数据')
    } else {
      ajaxNew('/business/api.v1.center/item/modBatch', params, 'post')
        .then((res) => {
          if (res.resultCode === '0') {
            this.modifyDialog = {
              isVisible: false,
              value: '',
              key: '',
              type: 0, // 显示方式1:文本,2:多行文本,3:下拉,4:下拉多选,5:数字文本
            }
            this.modifyDialogTable = []
            this.modifyItem = []
            this.changeEnum = []
            this.qryItemList()
          } else {
            this['$message'].error(res.resultMsg)
          }
        })
        .catch((e) => {
          console.log(e)
        })
    }
  }

  // 复制提交
  submitCopy(): void {
    const itemName = this.isTrue == 1 ? `（复制关联逻辑）${this.copyDialog.itemName}` :  `（复制）${this.copyDialog.itemName}`
    itemCopy({ itemId: this.copyDialog.itemId, isTrue: this.isTrue, itemName }).then(
      (res) => {
        if (res) {
          this['$message'].success('复制成功')
          this.copyDialog.isVisible = false
          this.qryItemList()
        }
      }
    )
  }

  // 富文本返回值
  emitInput(val: any): void {
    console.log(val)
    this.dialog[val.key] = val.value
  }

  mounted() {
    this.questionId = this['$route'].query.questionId as string
    this.subjectNo = this['$route'].query.subjectNo as string
    this.optionNo = this['$route'].query.optionNo as string
    this.qryItemList()
    this.getBuList()
    this.getLaboratoryList('', 'laboratoryListAll')
    this.getBusinessListAll()
    this.getSampleCategoryList()
  }
}
export default TestItem
</script>

<style lang='less' scoped>
.show_tooltip {
  width: 100%;
  height: 25px;
  overflow: hidden;
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.download {
  color: #409eff;
  text-decoration: none;
  font-size: 12px;
  padding: 0 15px;
}
</style>
<style>
.el-table--small td,
.el-table--small th {
  padding: 8px 0 !important;
}
</style>

