<template>
  <el-row>
    <el-col :span="24">
      <RelItem style="margin-bottom: 20px;" v-if="isShow" />
      <AllReltem :isQuestionList="isQuestionList" />
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import RelItem from "./Reltem.vue";
import AllReltem from "./AllReltem.vue";

@Component({
  name: "TestItem",
  components: {
    RelItem,
    AllReltem,
  },
})
class TestItem extends Vue {
  private isShow = false;
  // 是否问卷列表
  private isQuestionList = true;
  mounted() {
    // 菜单进入，隐藏被关联的测试项目
    if (Object.keys(this["$route"].query).length >= 1) {
      this.isShow = true;
    }
    if (Object.keys(this["$route"].query).length > 1) {
      this.isQuestionList = false;
    }
  }
}
export default TestItem;
</script>

<style lang='less' scope>
</style>
