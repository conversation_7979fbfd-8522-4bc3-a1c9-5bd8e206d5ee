<template>
  <div class="layout">
    <el-container>
      <el-aside width="200px">
        <div class="user_info">
          <span>您好，{{userName}}</span>
          <el-button @click="logout" type="text">退出登录</el-button>
        </div>
        <div v-if="envName" style="text-align: center;background: rgb(84, 92, 100);padding: 5px 0;color: #fff;">{{envName}}</div>
        <el-menu router
                 :default-active="menuActive"
                 class="el-menu-vertical-demo"
                 background-color="#545c64"
                 text-color="#fff"
                 active-text-color="#ffd04b">
          <el-submenu :index="String(i + 1)"
                      v-for="(v, i) of menus"
                      :key="i">
            <template slot="title">
              <i class="el-icon-location"></i>
              <span>{{ v.funcName }}</span>
            </template>
            <el-menu-item :index="v1.funcUrl"
                          v-for="(v1, i1) of v.children"
                          :key="i1">{{ v1.funcName }}</el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>
      <el-main>
        <!-- <div id="centerWeb-vue3"
             style="width: 100%;border: 1px solid #f00;"></div> -->
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { ajaxNew } from './../api/index'
import {
  createTreeData,
  CurrentSystem,
  local,
} from './../assets/javascript/util'

@Component({
  name: 'Frame',
})
class Frame extends Vue {
  // watch router
  @Watch('$route')
  oncuechange(to) {
    this.menuActive = to.path
  }
  // menu active
  private menuActive = ''
  private menus = []

  private userName = ''
  private envName = ''

  logout(): void {
    this['$confirm'](`您确认退出？`)
      .then(() => {
        local.clear()
        this['$router'].push('/login')
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 获取系统菜单 // todo 根据登录用户获取菜单列表
  getSystemList(): void {
    ajaxNew(
      '/business/api.v1.sso/sysperson/showAuth',
      {
        system: CurrentSystem,
      },
      'post',
      {
        type: 'ticSso',
      }
    )
      .then((res) => {
        if (res.resultCode === '0') {
          // 数据排序
          res.data.funcItem.sort((a, b) => {
            return a.sortShow - b.sortShow
          })
          this.menus = createTreeData(res.data.funcItem, 'funcId', 'parentId')
        } else {
          this['$message']({
            message: res.resultMsg,
            type: 'error',
          })
        }
      })
      .catch((error) => {
        this['$message']({
          message: error,
          type: 'error',
        })
      })
  }

  mounted() {
    this.getSystemList()
    this.userName = local.get('token').userName
    if (process.env.NODE_ENV !== 'prodGray' && process.env.NODE_ENV !== 'prod' && process.env.NODE_ENV !== 'product') {
      this.envName = '测试环境-' + process.env.NODE_ENV
    }

  }
}
export default Frame
</script>

<style scoped>
.layout,
.el-container {
  height: 100vh;
}

.el-aside {
  background-color: #d3dce6;
  width: 200px;
  overflow: hidden;
}

.el-main {
  background-color: #e9eef3;
}

.user_info {
  background-color: rgb(84, 92, 100);
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.user_info * {
  color: #fff;
  font-size: 12px;
}
</style>