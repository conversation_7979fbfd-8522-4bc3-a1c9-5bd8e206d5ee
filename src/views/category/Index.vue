<template>
  <el-row>
    <el-col :span="14">
      <el-select v-model="formItem.catagoryId" filterable clearable placeholder="类目快捷搜索..." @change="categoryChange"
        style="width: 400px; margin-right: 20px" @blur="getCatrgoryList">
        <el-option v-for="item in qryItems" :key="item.catagoryId" :label="item.catagoryPath"
          :value="item.catagoryId"></el-option>
      </el-select>
      <el-button @click="getCatrgoryList">搜索</el-button>
    </el-col>
    <el-col :span="10" style="text-align: right">
      <el-button type="primary" @click="handleAddCategory">文字导入</el-button>
      <el-upload :on-success="onSuccess" :show-file-list="uploadFile.showFileList" :action="uploadFile.host +
        'ticCenter/business/api.v0.platform/fileUpload/uploadOss'
        " style="margin-left: 20px; float: right">
        <el-button>模板导入</el-button>
      </el-upload>
    </el-col>
    <el-col :span="24" style="padding-top: 10px">
      <!-- default-expand-all -->
      <div class="custom-tree-title">
        <span>类目</span>
        <span>所属问卷</span>
      </div>
      <!-- <el-tree
        :data="trees"
        node-key="id"
        draggable
        @node-drag-start="handleDragStart"
        @node-drag-end="handleDragEnd"
      > -->
      <el-tree v-loading="loading" :data="trees" node-key="catagoryId" :expand-on-click-node="false"
        @node-drag-start="handleDragStart" @node-drag-end="handleDragEnd" :default-expanded-keys="defaultExpandedKeys"
        @node-expand="nodeExpand" @node-collapse="nodeCollapse">
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div class="row-category">
            <span>{{ data.catagoryName }}</span>
            <span>
              <el-button size="small" @click="() => move(node, data, 'top')" icon="el-icon-top" circle></el-button>
              <el-button size="small" @click="() => move(node, data, 'bottom')" icon="el-icon-bottom"
                circle></el-button>
              <el-button size="small" @click="() => append(data)" icon="el-icon-plus">添加子类</el-button>
              <el-button size="small" @click="() => modify(data)" icon="el-icon-edit">修改</el-button>
              <el-button size="small" @click="() => remove(data)" icon="el-icon-delete">删除</el-button>
              <el-button size="small" v-if="!data.parentId" @click="() => addProcess(data)" icon="el-icon-plus">添加流程</el-button>
              <!-- <el-button size="small"
                         @click="() => sample(data)"
                         icon="el-icon-s-grid">样品字段</el-button> -->
            </span>
          </div>
          <div class="row-qs">
            <template v-if="data.questionName">
              <el-tooltip class="item" effect="dark" :content="data.questionName" placement="top-end">
                <el-button type="text" size="small" @click="handleQuestionName(data.questionId)">{{ data.questionName
                  }}</el-button>
              </el-tooltip>
              <el-button size="small" @click="handleQsMod(data)">修改问卷</el-button>
            </template>
            <template v-else>
              <span></span>
              <el-button size="small" @click="handleQsAdd(data)">添加问卷</el-button>
            </template>
          </div>
        </div>
      </el-tree>
    </el-col>

    <el-dialog title="文字导入" :visible.sync="categoryTextDialog.visible" width="30%">
      <el-input type="textarea" :rows="20" v-model="categoryTextDialog.text"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="categoryTextDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleTextImport">导 入</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="categoryDialog.title" :visible.sync="categoryDialog.visible" width="500px">
      <el-input maxlength="50" type="text" v-model="categoryModify.catagoryName"></el-input>
      <div class="category-img" v-if="!categoryModify.parentId && categoryDialog.title.includes('修改')">
        <img :src="categoryModify.categoryImage" alt=""> 
        <el-button-group>
          <el-button @click="handleUplodaImg">上传图片</el-button>
          <el-button @click="handleDeleteImg">删除图片</el-button>
        </el-button-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="categoryDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="修改/添加问卷" :visible.sync="categoryModifyDialog.visible" width="30%">
      将类目关联到问卷：
      <el-select v-model="categoryModify.questionId">
        <el-option v-for="(item, index) of questionList" :label="item.questionName" :key="index"
          :value="item.questionId"></el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="categoryModifyDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleCatrgoryMod">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="请选择需要的样品信息字段并设置排序" :visible.sync="sampleItem.visible" width="60%" class="sample">
      <el-row>
        <el-checkbox-group v-model="checkList">
          <Draggable v-model="sampleItem.datas">
            <el-col :span="6" v-for='(item, index) of sampleItem.datas' :key='index'>
              <el-checkbox :disabled='!!item.isFill' :label="item.enumCode">{{ item.enumName }}</el-checkbox>
            </el-col>
          </Draggable>
        </el-checkbox-group>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleSampleCancle">取 消</el-button>
        <el-button type="primary" @click="handleSampleSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源上传"
               :visible.sync="uploadFileDialog.visible"
               width="500px"
               :modal-append-to-body="true"
               :append-to-body="true">
      <FileUpload :imgPath="uploadFileDialog"
                  @childSend="childSendfun" />
    </el-dialog>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { ajaxNew } from './../../api/index'
import { createTreeData, apiHost } from './../../assets/javascript/util'
import { qryEnum, qryByBusi, modSample } from './../../api/common'
import FileUpload from '../../components/FileUpload.vue'
import Draggable from "vuedraggable";

@Component({
  name: 'Category',
  components: {
    Draggable,
    FileUpload
  },
})
class Category extends Vue {
  // 类目编辑、新增的弹窗及内容
  private categoryDialog = {
    visible: false,
    title: '',
  }

  //类目文本导入
  private categoryTextDialog = {
    text: '',
    visible: false,
  }

  // 修改类目的弹窗
  private categoryModifyDialog = {
    visible: false,
    type: '',
  }

  // 上传文件
  private uploadFile = {
    showFileList: false,
    host: apiHost,
  }

  // 类目列表
  private qryItems = []

  // 搜索类目
  private searchValue = ''

  private trees = []

  // 获取类目数据参数
  private formItem = {
    // catagoryPath: "",
    questionId: '',
    catagoryId: '',
  }

  // 修改类目参数
  private categoryModify = {
    // questionName: "",
    catagoryName: '',
    parentId: '',
    catagoryId: '',
    sortShow: '',
    catagoryLevel: '',
    questionId: '',
    categoryImage: '',
  }

  // 问卷列表
  private questionList = []

  // 默认展开
  private defaultExpandedKeys = []

  private loading = false

  // 动态样品字段
  private sampleItem = {
    visible: false,
    datas: [],
  }
  private defaultCheckList = []
  private checkList = []
  private businessId = ''
  private disableGroups = []

  // 上传文件弹窗
  private uploadFileDialog = {
    imgPath: '',
    visible: false,
    iconPath: '',
    index: 0,
  }
  categoryChange(data: object): void {
    console.log(data)
    console.log(this.formItem)
  }

  // 文字导入
  handleAddCategory(): void {
    this.categoryTextDialog.visible = true
    this.categoryTextDialog.text = ''
  }

  // 分类查询
  getCatrgoryList() {
    ajaxNew('/business/api.v1.center/catagory/qry', this.formItem, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          // 生成类目快捷搜索
          this.qryItems = res.data.items.filter((item) => {
            return item.isEnd
          })
          this.trees = createTreeData(res.data.items, 'catagoryId', 'parentId')
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 文本导入
  handleTextImport() {
    const param = []
    this.categoryTextDialog.text.split('\n').forEach((item) => {
      param.push({
        catagoryPath: item,
      })
    })

    ajaxNew('/business/api.v1.center/catagory/addBatch', param, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.categoryTextDialog.visible = false
          this.getCatrgoryList()
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  onSuccess(res) {
    if (res.resultCode === '0') {
      this.fileImport(res.data.fileName)
    } else {
      this['$message'].error(res.resultMsg)
    }
  }

  // 文件导入
  fileImport(fileName) {
    ajaxNew('/business/api.v1.center/catagory/imp', { fileName }, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this['$message'].success('模板导入成功')
          this.getCatrgoryList()
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 拖动开始
  handleDragStart(node) {
    const data = node && node.data
    // console.log(data)
    this.categoryModify.catagoryName = data.catagoryName
    this.categoryModify.catagoryId = data.catagoryId
  }

  // 拖动结束
  handleDragEnd(dropNode, endNode, dropType, event): void {
    const data = dropNode && dropNode.data
    console.log(data, endNode.data, dropType, event)
    this.categoryModify.sortShow = data.sortShow
    if (dropType === 'inner') {
      this.categoryModify.catagoryLevel = data.catagoryLevel + 1
    } else {
      this.categoryModify.catagoryLevel = data.catagoryLevel
    }
    if (data.parentId) {
      if (dropType === 'none') {
        this.categoryModify.parentId = endNode.data.parentId
      } else if (dropType === 'before' || dropType === 'after') {
        this.categoryModify.parentId = endNode.data.parentId
      } else if (dropType === 'inner') {
        this.categoryModify.parentId = endNode.data.catagoryId
      }
    } else {
      this.categoryModify.parentId = endNode.data.parentId
    }
    // this.catrgoryMod();
    this.catrgoryModDrag()
  }

  // 拖动修改
  catrgoryModDrag(): void {
    console.log('this.categoryModify', this.categoryModify)
    ajaxNew(
      '/business/api.v1.center/catagory/drag',
      {
        catagoryName: this.categoryModify.catagoryName,
        parentId: this.categoryModify.parentId,
        catagoryId: this.categoryModify.catagoryId,
        sortShow: this.categoryModify.sortShow,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.categoryDialog.visible = this.categoryModifyDialog.visible =
            false
          this.getCatrgoryList()
        } else {
          this['$message']({
            message: res.resultMsg,
            type: 'error',
            duration: 500,
            onClose: () => {
              this.getCatrgoryList()
            },
          })
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 修改类目
  handleCatrgoryMod(): void {
    console.log(this.categoryModifyDialog.type)
    if (this.categoryModifyDialog.type === 'add') {
      // this.catrgoryMod();
      this.setQuestion()
    } else {
      this.setQuestion()
    }
  }
  catrgoryMod(): void {
    delete this.categoryModify.questionId
    ajaxNew('/business/api.v1.center/catagory/mod', this.categoryModify, 'post')
      .then((res) => {
        if (res.resultCode === '0') {
          this.categoryDialog.visible = this.categoryModifyDialog.visible =
            false
          this.getCatrgoryList()
        } else {
          this['$message']({
            message: res.resultMsg,
            type: 'error',
            duration: 500,
            onClose: () => {
              this.getCatrgoryList()
            },
          })
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 添加节点
  append(data) {
    this.categoryDialog.visible = true
    this.categoryDialog.title = '添加类目名称'
    this.categoryModify.catagoryName = ''
    this.categoryModify.parentId = data.parentId
    this.categoryModify.catagoryId = data.catagoryId
    //delete this.categoryModify.questionId
  }

  // 修改节点
  modify(data) {
    this.categoryDialog.visible = true
    this.categoryDialog.title = '修改类目名称'
    this.categoryModify.catagoryName = data.catagoryName
    this.categoryModify.parentId = data.parentId
    this.categoryModify.catagoryId = data.catagoryId
    this.categoryModify.categoryImage = data.categoryImage
    //delete this.categoryModify.questionId
  }

  // 移动节点
  move(node, data, direction): void {
    const find = (arr, catagoryId) => {
      arr.forEach((item, index) => {
        if (item.catagoryId === catagoryId) {
          // 第一个和最后一个不做处理
          if (!index && direction === 'top') {
            this['$message'].error('已经是第一个了')
            return
          } else if (index === arr.length - 1 && direction === 'bottom') {
            this['$message'].error('已经是最后一个了')
            return
          } else {
            let otherCatagoryId = 0
            // dom间的移动
            if (direction === 'top') {
              otherCatagoryId = arr[index - 1].catagoryId
              arr[index] = arr.splice(index - 1, 1, arr[index])[0]
            } else if (direction === 'bottom') {
              otherCatagoryId = arr[index + 1].catagoryId
              // arr[index] = arr.splice(index + 1, 1, arr[index])[0];
            }
            // console.log(catagoryId, otherCatagoryId);
            this.trees = JSON.parse(JSON.stringify(this.trees))
            this.transSort(catagoryId, otherCatagoryId)
          }
        } else if (item.children && item.children.length > 0) {
          find(item.children, catagoryId) //递归调用
        }
      })
    }
    find(this.trees, data.catagoryId)
    // const item = find(this.trees, data.catagoryId);
    // console.log('returnedItem', returnedItem);
  }

  transSort(catagoryId: number, otherCatagoryId: number): void {
    this.loading = true
    ajaxNew(
      '/business/api.v1.center/catagory/transSort',
      {
        catagoryId,
        otherCatagoryId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          // this["$message"].success("修改成功");
          this.getCatrgoryList()
        } else {
          this['$message'].error(res.resultMsg)
        }
        this.loading = false
      })
      .catch((e) => {
        console.log(e)
        this.loading = false
      })
  }

  handleDialogSubmit() {
    console.log(
      'this.categoryModify.catagoryName',
      this.categoryModify.catagoryName
    )
    if (this.categoryModify.catagoryName.includes('/')) {
      this['$message'].error('类目名称不能包含“/”，请修改后提交')
    } else {
      if (this.categoryDialog.title === '修改类目名称') {
        this.catrgoryMod()
      } else {
        ajaxNew(
          '/business/api.v1.center/catagory/add',
          {
            catagoryName: this.categoryModify.catagoryName,
            parentId: this.categoryModify.catagoryId,
          },
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this.categoryDialog.visible = false
              this.getCatrgoryList()
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            console.log(e)
          })
      }
    }
  }

  // 删除节点
  remove(data): void {
    this['$confirm'](`所有子类和关联问卷的关系都会删除.`)
      .then(() => {
        ajaxNew(
          '/business/api.v1.center/catagory/del',
          {
            catagoryId: data.catagoryId,
          },
          'post'
        )
          .then((res) => {
            if (res.resultCode === '0') {
              this['$message'].success('删除成功')
              this.getCatrgoryList()
            } else {
              this['$message'].error(res.resultMsg)
            }
          })
          .catch((e) => {
            console.log(e)
          })
      })
      .catch((e) => {
        console.log(e)
      })
  }

  // 添加流程 
  addProcess(data: any) {
    // 进入step0 编排页面
    this.$router.push({
        path: 'step0',
        query: {
          businessId: data.catagoryId,
          businessType: 'CATEGORY',
          categoryName: data.catagoryName,
        },
    })
  }

  // 编辑动态样品
  sample(data): void {
    this.businessId = data.catagoryId
    this.checkList = JSON.parse(JSON.stringify(this.defaultCheckList))
    console.log(this.checkList)
    qryByBusi({ businessType: 'CATEGORY', businessId: data.catagoryId }).then(
      (res) => {
        if (res.length) {
          res.forEach((v) => {
            this.checkList.push(v.sampleKey)
          })
        }
        this.sampleItem.visible = true
      }
    )
  }
  handleSampleCancle() {
    this.sampleItem.visible = false
  }
  handleSampleSubmit() {
    let arr = []
    let sortShow = 0
    this.sampleItem.datas.forEach((v) => {
      if (this.checkList.includes(v.enumCode)) {
        sortShow++
        arr.push({
          businessType: 'CATEGORY', // 业务类型
          businessId: this.businessId, // 业务ID
          sampleKeyName: v.enumName, // 样品KEY名称
          sampleKey: v.enumCode, // 样品KEY
          isMust: 0, // 是否必填
          isMerge: 1, // 是否合并
          sortShow, // 排序
        })
      }
    })
    modSample(arr).then((res) => {
      if (res) {
        this['$message'].success('保存成功')
        this.sampleItem.visible = false
      }
    })
  }

  // 添加问卷
  handleQsAdd(data): void {
    this.tempFun(data)
    this.categoryModifyDialog.type = 'add'
  }

  // 修改问卷
  handleQsMod(data): void {
    this.categoryModifyDialog.type = 'modify'
    if (!data.isEnd) {
      this['$confirm'](
        `您确认修改《${data.questionName}》问卷，会应用所有下级类目？`
      )
        .then(() => {
          this.tempFun(data)
        })
        .catch((e) => {
          console.log(e)
        })
    } else {
      this.tempFun(data)
    }
  }
  tempFun(data): void {
    Object.keys(this.categoryModify).forEach((item) => {
      this.categoryModify[item] = ''
    })
    this.categoryModify.catagoryName = data.catagoryName
    this.categoryModify.parentId = data.parentId
    this.categoryModify.catagoryId = data.catagoryId
    this.categoryModify.questionId =
      data.questionId || this.questionList[0].questionId
    this.categoryModify.sortShow = data.sortShow
    this.categoryModify.catagoryLevel = data.catagoryLevel
    this.categoryModifyDialog.visible = true
  }

  // 修改问卷（包含子类）
  setQuestion() {
    ajaxNew(
      '/business/api.v1.center/catagory/setQuestion',
      {
        // questionName: this.categoryModify.questionName,
        questionId: this.categoryModify.questionId,
        catagoryId: this.categoryModify.catagoryId,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.categoryDialog.visible = this.categoryModifyDialog.visible =
            false
          this.getCatrgoryList()
        } else {
          this['$message'].error(res.resultMsg)
        }
      })
      .catch((e) => {
        this['$message'].error(e)
      })
  }

  // 获取问卷列表
  getQustionList() {
    this.loading = true
    ajaxNew(
      '/business/api.v1.question/question/qry',
      {
        pageNum: 1,
        pageRow: 999,
      },
      'post'
    )
      .then((res) => {
        if (res.resultCode === '0') {
          this.questionList = res.data.items
        } else {
          this['$message'].error(res.resultMsg)
        }
        this.loading = false
      })
      .catch((e) => {
        this['$message'].error(e)
        this.loading = false
      })
  }

  // 节点被展开或收起
  nodeExpand(p1, p2, p3) {
    console.log('expand', p1, p2, p3)
    if (!this.defaultExpandedKeys.includes(p1.catagoryId))
      this.defaultExpandedKeys.push(p1.catagoryId)
    console.log(this.defaultExpandedKeys)
  }
  nodeCollapse(p1, p2, p3) {
    console.log('collapse', p1, p2, p3)
    if (this.defaultExpandedKeys.includes(p1.catagoryId))
      this.defaultExpandedKeys.splice(
        this.defaultExpandedKeys.findIndex(
          (item) => item.catagoryId === p1.catagoryId
        ),
        1
      )
    console.log(this.defaultExpandedKeys)
  }

  // 点击问卷名跳转但问卷
  handleQuestionName(id): void {
    window.open(`/step1?id=${id}`)
  }

  // 接收子组件的返回值
  childSendfun(res: string) {
    console.log(res,'aaaaaaaaaaaaaaaa')
    debugger
    this.categoryModify.categoryImage = res
  }

  // 上传图片
  handleUplodaImg(): void {
    this.uploadFileDialog.visible = true
    // this.uploadFileDialog.imgPath = iconPath
    // this.uploadFileDialog.index = index
  }

  // 删除图片
  handleDeleteImg(): void {
    this.categoryModify.categoryImage = ''
  }
  mounted() {
    this.getCatrgoryList()
    this.getQustionList()
    qryEnum({
      tableName: 'SAMPLE_BASIC',
      colName: 'SAMPLE_KEY',
    }).then((res) => {
      this.sampleItem.datas = res
      // 通过枚举值获取默认选中的数据
      res.forEach((v) => {
        if (v.isFill) this.defaultCheckList.push(v.enumCode)
      })
      console.log(this.defaultCheckList)
    })
  }
}
export default Category
</script>

<style lang='less' scope>
.custom-tree-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #fff;
  border-bottom: 1px dotted #ccc;

  span {
    display: flex;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: #409eff;

    &:first-child {
      flex: 1;
      border-right: 1px solid #ccc;
    }

    &:last-child {
      width: 200px;
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 3px 5px;

  .row-qs {
    display: flex;
    width: 200px;
    justify-content: space-between;
    align-items: center;

    button {
      &:first-child {
        max-width: 120px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .row-category {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    margin-right: 15px;
  }
}

.el-tree {
  padding: 10px;
}

.el-tree-node__content {
  height: auto;
}
.category-img {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: flex-end;

  img {
    width: 460px;
    margin-bottom: 10px;
  }
}
</style>
<style>
.sample .el-col-6 {
  margin-bottom: 15px;
}
</style>
