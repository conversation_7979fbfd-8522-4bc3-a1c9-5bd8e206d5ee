<template>
  <div class="preview">
    <header>
      <div class="must" style="font-size: 12px;">提示： 此为预览页面，不能参与作答！</div>
      <div class="priview_tabs">
        <el-button
          @click="handleTab(tab.type)"
          :type="tab.type === activeTab ? 'primary' : ''"
          :icon="'el-icon-'+ tab.type"
          v-for="(tab, index) of tabs"
          :key="index"
        >{{ tab.name }}</el-button>
      </div>
      <div>
        <el-checkbox disabled>测试作答</el-checkbox>
        <el-button type="primary" style="margin-left: 20px;" disabled>发布问卷</el-button>
      </div>
    </header>
    <div class="preivw_warp">
      <div class="preview_mobile" v-if="activeTab === 'mobile'">
        <iframe frameborder="0" :src="previewHost + '/questionnaire/step1?category=' + category"></iframe>
      </div>
      <div class="preview_pc" v-if="activeTab === 's-platform'">
        <iframe frameborder="0" :src="previewHost + '/questionnaire/step1?category=' + category"></iframe>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { previewHost } from "./../assets/javascript/util";

@Component({
  name: "Preview"
})
class Preview extends Vue {
  private tabs = [
    {
      name: "手机预览",
      type: "mobile"
    },
    {
      name: "电脑预览",
      type: "s-platform"
    },
    {
      name: "关闭预览",
      type: "close"
    }
  ];
  private activeTab = "s-platform";
  private category = "";
  private previewHost = previewHost;

  handleTab(type: string): void {
    if (type !== "close") {
      this.activeTab = type;
    } else {
      window.close();
    }
  }

  mounted() {
    this.category = this["$route"].query.category as string;
  }
}
export default Preview;
</script>

<style lang='less' scoped>
.preview {
  header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    background-color: #fff;
    -moz-box-shadow: 0 0 15px 0 rgba(29, 50, 71, 0.1);
    -webkit-box-shadow: 0 0 15px 0 rgba(29, 50, 71, 0.1);
    box-shadow: 0 0 15px 0 rgba(29, 50, 71, 0.1);
    height: 70px;
    display: flex;
    margin: 0 auto;
    height: 70px;
    justify-content: space-between;
    align-items: center;

    div {
      flex: 1;
      justify-content: center;
      align-items: center;
      display: flex;
    }
  }
  .preivw_warp {
    margin-top: 70px;
    padding: 50px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .preview_mobile {
      background: url("./../assets/images/image-phone.png") 0 0;
      width: 438px;
      height: 762px;

      iframe {
        margin: 40px 0 0 59px;
        width: 320px;
        height: 620px;
      }
    }
    .preview_pc {
      width: 1244px;
      height: 762px;
      iframe {
        width: 1244px;
        height: 762px;
      }
    }
  }
}
</style>