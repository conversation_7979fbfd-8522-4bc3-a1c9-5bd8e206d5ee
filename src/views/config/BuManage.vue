<template>
  <el-row>
    <el-col :span="24">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>基础数据</el-breadcrumb-item>
        <el-breadcrumb-item>BU维护</el-breadcrumb-item>
      </el-breadcrumb>
      <el-tabs type="border-card"
               v-model="activeName">
        <el-tab-pane label="Tic CenterWeb && OrderWeb"
                     name="ticCenter">
          <ConfigTree :from="from"
                      :system='activeName'
                      v-if='activeName === "ticCenter"' />
        </el-tab-pane>
        <el-tab-pane label="LeadsWeb"
                     name="ticLeads">
          <ConfigTree :from="from"
                      :system='activeName'
                      v-if='activeName === "ticLeads"' />
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import ConfigTree from './../../components/ConfigTree.vue'

@Component({
  name: 'BuManage',
  components: {
    ConfigTree,
  },
})
class BuManage extends Vue {
  private from = 'bu' // 传递给组件，记录页面的来源
  private activeName = 'ticCenter'
}
export default BuManage
</script>