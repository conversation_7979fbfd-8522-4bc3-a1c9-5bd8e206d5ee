<template>
  <el-row>
    <el-col :span="24">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>基础数据</el-breadcrumb-item>
        <el-breadcrumb-item>标签配置</el-breadcrumb-item>
      </el-breadcrumb>
    </el-col>
    <el-form ref="form"
             :model="qryItemParam"
             label-width="100px"
             :inline="true"
             size="">
      <el-col :span="6">
        <el-form-item label="">
          <el-input placeholder="请输入标签名称..."
                    type="text"
                    v-model="qryItemParam.labelName"
                    clearable
                    style="width: 100%;"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="">
          <el-input placeholder="请输入描述..."
                    type="text"
                    v-model="qryItemParam.labelDesc"
                    clearable
                    style="width: 100%;"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12"
              style="margin-bottom: 10px; text-align: right;">
        <el-button type="primary"
                   size=""
                   @click="qryLabel">查询</el-button>
        <el-button type="primary"
                   size=""
                   @click="handleAdd"
                   v-btnAuth='1501'>添加</el-button>
      </el-col>
    </el-form>
    <el-col :span="24">
      <el-table :data="datas"
                border
                stripe
                size=''>
        <el-table-column label="名称"
                         prop="labelName"
                         width="200"
                         align="center"></el-table-column>
        <el-table-column label="描述"
                         prop="labelDesc"
                         width="400"
                         align="center"></el-table-column>
        <el-table-column label="客户端提示文案"
                         prop="fillPrompt"
                         width="200"
                         align="center"></el-table-column>
        <el-table-column label="选项"
                         prop="labelOptionList"
                         align="center">
          <template slot-scope="scope">
            {{ scope.row.labelOptionList && scope.row.labelOptionList.map(item => item.optionName).join(',')}}
          </template>
        </el-table-column>
        <el-table-column label="是否可编辑"
                         width="100"
                         prop="isEdit"
                         align="center">
          <template slot-scope="scope">
            {{ scope.row.isEdit == 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="操作"
                         width="200"
                         align='center'>
          <template slot-scope="scope"
                    v-if="scope.row.isEdit">
            <el-button type="primary"
                       size=''
                       @click="handleEidt(scope.row)">编辑</el-button>
            <el-button size=''
                       @click="handleDel(scope.row)"
                       v-btnAuth='1502'>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <LabelDialog :system="system"
                   :props="labelProps" />
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import ConfigTree from '../../components/ConfigTree.vue'
import { qryTag, delTag } from '../../api/common'
import LabelDialog from '../../components/config/label/Dialog.vue'

@Component({
  name: 'LabelConfig',
  components: {
    ConfigTree,
    LabelDialog,
  },
})
class Label extends Vue {
  @Mutation('setLabel') setLabel
  private from = 'label' // 传递给组件，记录页面的来源
  private datas = []
  private labelProps = {
    visible: false,
    data: {},
  }
  private form = {}
  private qryItemParam = {}
  private system = 'ticCenter'

  qryLabel() {
    qryTag(this.qryItemParam).then((res) => {
      this.datas = res
    })
  }

  handleAdd(): void {
    this.setLabel({
      visible: true,
      data: {},
    })
  }
  handleEidt(row): void {
    this.setLabel({
      visible: true,
      data: row,
    })
  }
  handleDel(row): void {
    this.$confirm(`您确认删除  ${row.labelName} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        delTag({ labelId: row.labelId, labelCode: row.labelCode }).then((res) => {
          if (res === true) {
            this.$message({
              type: 'success',
              message: `删除成功`,
            })
            this['message']
            this.qryLabel()
          } else {
            this.$alert(res, '提示', {
              confirmButtonText: '确定',
              callback: (action) => {
                console.log(action)
              },
            })
          }
        })
      })
      .catch(() => {})
  }

  emitDialog(val) {
    this.labelProps.visible = false
  }

  mounted() {
    this.qryLabel()
  }
}
export default Label
</script>