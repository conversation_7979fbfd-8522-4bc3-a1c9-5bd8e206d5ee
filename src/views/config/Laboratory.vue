<template>
  <el-row>
    <el-col :span="24">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>基础数据</el-breadcrumb-item>
        <el-breadcrumb-item>实验室维护</el-breadcrumb-item>
      </el-breadcrumb>
    </el-col>
    <el-form ref="form"
             :model="qryItemParam"
             label-width="100px"
             :inline="true"
             size="small">
      <el-col :span="10">
        <!-- <el-form-item label="BU">
          <el-input
            placeholder="请输入BU..."
            type="text"
            v-model="qryItemParam.bu"
            clearable
          ></el-input>
        </el-form-item> -->
      </el-col>
      <el-col :span="10">
        <!-- <el-form-item label="业务线">
          <el-input
            placeholder="请输入业务线..."
            type="text"
            v-model="qryItemParam.businessLineId"
            clearable
          ></el-input>
        </el-form-item> -->
      </el-col>
      <el-col :span="24"
              style="margin-bottom: 10px;">
        <el-button type="primary"
                   size="small"
                   @click="handleAdd"
                   v-btnAuth='1401'>新增实验室</el-button>
      </el-col>
    </el-form>
    <el-col :span="24">
      <el-table :data="datas"
                border
                stripe
                size='small'>
        <el-table-column label="实验室名称"
                         prop="labName"></el-table-column>
        <el-table-column label="BU"
                         width="200"
                         prop="buName"></el-table-column>
        <el-table-column label="业务线"
                         width="200"
                         prop="lineName"></el-table-column>
        <el-table-column label="操作"
                         width="100"
                         align='center'>
          <template slot-scope="scope">
            <el-button type="primary"
                       size='small'
                       @click="handleEidt(scope.row)">编辑</el-button>
            <!-- <el-button type="primary" @click="handleDel(scope.row)"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <LaboratoryDialog :system="system"
                        :props="laboratoryProps" />
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Mutation } from 'vuex-class'
import ConfigTree from './../../components/ConfigTree.vue'
import { qryLaboratory } from './../../api/common'
import LaboratoryDialog from './../../components/config/laboratory/Dialog.vue'

@Component({
  name: 'Laboratory',
  components: {
    ConfigTree,
    LaboratoryDialog,
  },
})
class Laboratory extends Vue {
  @Mutation('setLaboratory') setLaboratory
  private from = 'laboratory' // 传递给组件，记录页面的来源
  private datas = []
  private laboratoryProps = {
    visible: false,
    data: {},
  }
  private form = {}
  private qryItemParam = {}
  private system = 'ticCenter'

  qryLab() {
    qryLaboratory({ system: this.system }).then((res) => {
      this.datas = res
    })
  }

  handleAdd(): void {
    // this.laboratoryProps = {
    //   visible: true,
    //   data: {},
    // };
    this.setLaboratory({
      visible: true,
      data: {},
    })
  }
  handleEidt(row): void {
    // this.laboratoryProps = {
    //   visible: true,
    //   data: row,
    // };
    this.setLaboratory({
      visible: true,
      data: row,
    })
  }
  // handleDel(row): void {
  //   console.log(row);
  // }

  emitDialog(val) {
    this.laboratoryProps.visible = false
  }

  mounted() {
    this.qryLab()
  }
}
export default Laboratory
</script>