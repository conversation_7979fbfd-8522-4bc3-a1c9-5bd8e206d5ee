<template>
  <div class="sample-classify-container">
    <div class="header">
      <h2>样品分类维护</h2>
      <el-button @click="closeWindow" type="primary" size="small">关闭窗口</el-button>
    </div>
    <div class="main-content">
      <el-row>
        <el-col :span="24">
          <!-- 搜索区域 -->
          <el-row style="margin: 20px 0;">
            <el-col :span="14">
              <span style="margin-right: 10px;">样品分类：</span>
              <el-input
                v-model="searchForm.categoryName"
                placeholder="请输入样品分类名称"
                style="width: 300px; margin-right: 20px"
                clearable>
              </el-input>
              <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-col>
            <el-col :span="10" style="text-align: right">
              <el-button type="primary" @click="handleAddCategory">添加分类</el-button>
            </el-col>
          </el-row>

          <!-- 树形结构标题 -->
          <div class="custom-tree-title">
            <span>分类名称</span>
            <span>操作</span>
          </div>

          <!-- 树形结构 -->
          <el-tree
            v-loading="loading"
            :data="categoryTree"
            node-key="categoryId"
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpandedKeys"
            @node-expand="nodeExpand"
            @node-collapse="nodeCollapse">
            <div class="custom-tree-node" slot-scope="{ node, data }">
              <div class="row-category">
                <span>{{ data.categoryName }}</span>
                <span class="button-group">
                  <el-button 
                    size="small" 
                    @click="() => move(node, data, 'up')" 
                    icon="el-icon-top" 
                    circle 
                    :loading="sortLoading"
                    :disabled="isFirstInLevel(node, data)">
                  </el-button>
                  <el-button 
                    size="small" 
                    @click="() => move(node, data, 'down')" 
                    icon="el-icon-bottom" 
                    circle 
                    :loading="sortLoading"
                    :disabled="isLastInLevel(node, data)">
                  </el-button>
                  <el-button size="small" @click="() => handleAddSubCategory(data)" icon="el-icon-plus">添加子类</el-button>
                  <el-button size="small" @click="() => handleEditCategory(data)" icon="el-icon-edit">修改</el-button>  
                  <el-button size="small" @click="() => handleDeleteCategory(data)" icon="el-icon-delete">删除</el-button>
                  <el-button size="small" @click="() => handleAddStandard(data)" icon="el-icon-plus">添加标准类型</el-button>
                </span>
              </div>
            </div>
          </el-tree>
        </el-col>

        <!-- 添加/修改分类弹窗 -->
        <el-dialog :title="categoryDialog.title" :visible.sync="categoryDialog.visible" width="500px" @close="handleDialogClose">
          <el-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef" label-width="120px">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input 
                v-model="categoryForm.categoryName" 
                placeholder="请输入分类名称"
                maxlength="50">
              </el-input>
            </el-form-item>
            <el-form-item label="分类编码" prop="categoryCode">
              <el-input 
                v-model="categoryForm.categoryCode" 
                placeholder="请输入分类编码"
                maxlength="50"
                :disabled="categoryDialog.isEdit">
              </el-input>
            </el-form-item>
            <el-form-item label="备注" prop="memo">
              <el-input 
                v-model="categoryForm.memo" 
                type="textarea" 
                placeholder="请输入备注"
                maxlength="500">
              </el-input>
            </el-form-item>
            <el-form-item label="外部系统ID" prop="externalId">
              <el-input 
                v-model="categoryForm.externalId" 
                placeholder="请输入外部系统ID"
                maxlength="50">
              </el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="categoryDialog.visible = false">取 消</el-button>
            <el-button type="primary" @click="handleCategorySubmit">保 存</el-button>
          </span>
        </el-dialog>

        <!-- 添加标准弹窗 -->
        <el-dialog title="添加标准类型" :visible.sync="standardDialog.visible" width="60%">
          <div v-if="allStandards.length > 0">
            <el-row>
              <el-checkbox-group v-model="selectedStandards">
                <el-col :span="8" v-for="item in allStandards" :key="item.enumCode">
                  <el-checkbox :label="item.enumCode">
                    {{ item.enumName }}
                  </el-checkbox>
                </el-col>
              </el-checkbox-group>
            </el-row>
          </div>
          <div v-else>
            <el-empty description="暂无可选标准类型"></el-empty>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="standardDialog.visible = false">取 消</el-button>
            <el-button type="primary" @click="handleStandardSubmit">确 定</el-button>
          </span>
        </el-dialog>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { qrySampleCategory, saveSampleCategory, delSampleCategory, qryEnum, qrySampleStandard, saveSampleStandard } from './../../api/common'
import { ajaxNew } from './../../api/index'
import { createTreeData } from './../../assets/javascript/util'

@Component({
  name: 'SampleClassify',
})
class SampleClassify extends Vue {
  // 页面参数
  private buId = ''
  private lineId = ''
  private configId = ''
  private configName = ''

  // 搜索表单
  private searchForm = {
    categoryName: ''
  }

  // 分类树数据
  private categoryTree = []
  private defaultExpandedKeys = []
  private loading = false
  private sortLoading = false // 排序加载状态

  // 分类弹窗
  private categoryDialog = {
    visible: false,
    title: '',
    isEdit: false
  }

  // 分类表单
  private categoryForm = {
    categoryId: '',
    categoryName: '',
    categoryCode: '',
    memo: '',
    externalId: '',
    parentCategoryId: 0,
    lineId: '',
    bu: ''
  }

  // 表单验证规则
  private categoryRules = {
    categoryName: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { max: 50, message: '分类名称不能超过50个字符', trigger: 'blur' }
    ],
    categoryCode: [
      { max: 50, message: '分类编码不能超过50个字符', trigger: 'blur' }
    ],
    memo: [
      { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
    ],
    externalId: [
      { max: 50, message: '外部系统ID不能超过50个字符', trigger: 'blur' }
    ]
  }

  // 标准弹窗
  private standardDialog = {
    visible: false
  }

  private allStandards = []
  private existingStandards = []
  private selectedStandards = []
  private currentCategoryCode = ''

  // 关闭窗口
  closeWindow(): void {
    window.close()
  }

  // 搜索
  handleSearch(): void {
    this.getCategoryList()
  }

  // 获取分类列表
  getCategoryList(): void {
    this.loading = true
    const params: any = {
      lineId: this.lineId
    }

    // 如果有搜索条件，添加到参数中
    if (this.searchForm.categoryName && this.searchForm.categoryName.trim()) {
      params.categoryName = this.searchForm.categoryName.trim()
    }

    qrySampleCategory(params).then((res) => {
      if (res && res.length) {
        this.categoryTree = this.buildTree(res)
      } else {
        this.categoryTree = []
      }
      this.loading = false
    }).catch(() => {
      this.loading = false
    })
  }

  // 构建树形结构
  buildTree(data): any[] {
    const tree = []
    const map = {}

    // 创建映射
    data.forEach(item => {
      map[item.categoryId] = { ...item, children: [] }
    })

    // 构建树
    data.forEach(item => {
      if (item.parentCategoryId === 0) {
        tree.push(map[item.categoryId])
      } else {
        if (map[item.parentCategoryId]) {
          map[item.parentCategoryId].children.push(map[item.categoryId])
        }
      }
    })

    return tree
  }

  // 判断是否为同级第一个
  isFirstInLevel(node, data): boolean {
    if (!data.parentCategoryId) {
      const currentIndex = this.categoryTree.findIndex(item => item.categoryId === data.categoryId)
      return currentIndex === 0
    } else {
      // 子节点 - 检查是否为父节点下的第一个子节点
      const siblings = node.parent.data.children || []
      if (siblings.length === 0) return false
      return siblings[0].categoryId === data.categoryId
    }
  }

  // 判断是否为同级最后一个
  isLastInLevel(node, data): boolean {
    if (!data.parentCategoryId) {
      const currentIndex = this.categoryTree.findIndex(item => item.categoryId === data.categoryId)
      return currentIndex === this.categoryTree.length - 1
    } else {
      // 子节点 - 检查是否为父节点下的最后一个子节点
      const siblings = node.parent.data.children || []
      if (siblings.length === 0) return false
      return siblings[siblings.length - 1].categoryId === data.categoryId
    }
  }

  // 添加分类
  handleAddCategory(): void {
    this.categoryDialog.visible = true
    this.categoryDialog.title = '添加分类'
    this.categoryDialog.isEdit = false
    this.resetCategoryForm()
  }

  // 添加子类
  handleAddSubCategory(data): void {
    this.categoryDialog.visible = true
    this.categoryDialog.title = '添加子类'
    this.categoryDialog.isEdit = false
    this.resetCategoryForm()
    this.categoryForm.parentCategoryId = data.categoryId
  }

  // 修改分类
  handleEditCategory(data): void {
    this.categoryDialog.visible = true
    this.categoryDialog.title = '修改分类'
    this.categoryDialog.isEdit = true
    this.categoryForm = {
      categoryId: data.categoryId,
      categoryName: data.categoryName,
      categoryCode: data.categoryCode,
      memo: data.memo,
      externalId: data.externalId,
      parentCategoryId: data.parentCategoryId,
      lineId: data.lineId,
      bu: data.bu
    }
  }

  // 重置表单
  resetCategoryForm(): void {
    this.categoryForm = {
      categoryId: '',
      categoryName: '',
      categoryCode: '',
      memo: '',
      externalId: '',
      parentCategoryId: 0,
      lineId: this.lineId,
      bu: this.buId
    }
  }

  // 弹窗关闭事件
  handleDialogClose(): void {
    // 清除表单验证状态
    this.$nextTick(() => {
      if (this.$refs.categoryFormRef) {
        (this.$refs.categoryFormRef as any).clearValidate()
      }
    })
  }

  // 提交分类表单
  handleCategorySubmit(): void {
    this.$refs.categoryFormRef.validate((valid) => {
      if (valid) {
        const params = { ...this.categoryForm }
        if (!this.categoryDialog.isEdit) {
          delete params.categoryId
        }

        saveSampleCategory(params).then((success) => {
          if (success) {
            this.$message.success('保存成功')
            this.categoryDialog.visible = false
            this.getCategoryList()
          }
        })
      }
    })
  }

  // 删除分类
  handleDeleteCategory(data): void {
    this.$confirm('是否需要删除此分类？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      delSampleCategory(data.categoryId).then((success) => {
        if (success) {
          this.$message.success('删除成功')
          this.getCategoryList()
        }
      })
    }).catch(() => {
      // 用户取消
    })
  }

  // 移动节点
  move(node, data, direction): void {
    this.sortLoading = true

    const params = {
      categoryId: data.categoryId,
      sortFlag: direction // 'up' 或 'down'
    }

    this.transSortCategory(params).then((success) => {
      if (success) {
        this.$message.success('排序成功')
        this.getCategoryList()
      }
      this.sortLoading = false
    })
  }

  // 添加排序接口方法
  transSortCategory(params): Promise<boolean> {
    return ajaxNew('/business/api.v1.center/sampleCategory/transSort', params, "post")
      .then((res) => {
        if (res.resultCode === "0") {
          return true
        } else {
          this.$message.error(res.resultMsg || '排序失败')
          return false
        }
      })
      .catch((e) => {
        this.$message.error('排序失败: ' + e)
        return false
      })
  }

  // 添加标准
  handleAddStandard(data): void {
    this.currentCategoryCode = data.categoryCode
    this.standardDialog.visible = true
    this.selectedStandards = []

    // 先获取当前分类已有的标准，再获取所有标准
    this.getExistingStandards(data.categoryCode).then(() => {
      this.getAllStandards()
    })
  }

  // 获取所有标准
  getAllStandards(): void {
    const params = {
      tableName: "SYS_CATEGORY_STANDARD",
      colName: this.configName
    }

    qryEnum(params).then((res) => {
      console.log('获取所有标准类型返回数据:', res)
      if (res) {
        this.allStandards = res
        console.log('所有标准类型列表:', this.allStandards)
        console.log('已有标准类型列表:', this.existingStandards)

        // 设置已选中的标准（包括已有的）
        this.selectedStandards = this.existingStandards.slice()
      } else {
        this.allStandards = []
        console.log('没有获取到标准类型数据')
      }
    }).catch((error) => {
      console.error('获取标准类型列表失败:', error)
      this.allStandards = []
    })
  }

  // 获取已有标准
  getExistingStandards(categoryCode): Promise<void> {
    const params = {
      categoryCode: categoryCode,
      lineId: this.lineId
    }

    return qrySampleStandard(params).then((res) => {
      console.log('获取已有标准类型返回数据:', res)
      if (res && res.length) {
        // 根据standardCode提取已有的标准
        this.existingStandards = res.map(item => item.standardCode)
        console.log('已有标准类型codes:', this.existingStandards)
      } else {
        this.existingStandards = []
        console.log('该分类暂无标准类型')
      }
    }).catch((error) => {
      console.error('获取已有标准类型失败:', error)
      this.existingStandards = []
    })
  }

  // 提交标准 - 修复：使用正确的参数格式
  handleStandardSubmit(): void {
    // 构建保存参数，按照新的格式
    const standards = this.selectedStandards.map(standardCode => {
      const standard = this.allStandards.find(item => item.enumCode === standardCode)
      return {
        standardCode: standardCode,
        standardName: standard ? standard.enumName : standardCode
      }
    })

    const params = {
      lineId: parseInt(this.lineId),
      bu: this.buId,
      categoryCode: this.currentCategoryCode,
      standards: standards
    }

    console.log('准备保存的标准类型参数:', params)

    // 调用保存接口
    saveSampleStandard(params).then((success) => {
      if (success) {
        this.$message.success('标准类型保存成功')
        this.standardDialog.visible = false
        // 刷新已有标准列表
        this.getExistingStandards(this.currentCategoryCode)
      }
    })
  }

  // 节点展开
  nodeExpand(data): void {
    if (!this.defaultExpandedKeys.includes(data.categoryId)) {
      this.defaultExpandedKeys.push(data.categoryId)
    }
  }

  // 节点收起
  nodeCollapse(data): void {
    const index = this.defaultExpandedKeys.findIndex(id => id === data.categoryId)
    if (index > -1) {
      this.defaultExpandedKeys.splice(index, 1)
    }
  }

  mounted() {
    // 获取URL参数
    const query = this.$route.query
    this.buId = query.buId as string || ''
    this.lineId = query.lineId as string || ''
    this.configId = query.configId as string || ''
    this.configName = query.configName as string || ''

    console.log('页面参数:', {
      buId: this.buId,
      lineId: this.lineId,
      configId: this.configId,
      configName: this.configName
    })

    // 初始化数据
    this.getCategoryList()
  }
}
export default SampleClassify
</script>

<style lang='less' scoped>
.sample-classify-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.custom-tree-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #fff;
  border-bottom: 1px dotted #ccc;

  span {
    display: flex;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: #409eff;

    &:first-child {
      flex: 1;
      border-right: 1px solid #ccc;
    }

    &:last-child {
      width: 500px;
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 8px 5px;

  .row-category {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    margin-right: 15px;

    .button-group {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;

      .el-button {
        margin-left: 0 !important;
        margin-bottom: 2px;
      }
    }
  }
}

.el-tree {
  padding: 10px;
}

</style>

<style>
.el-tree-node__content {
  height: 26px;
  min-height: 32px;
  padding: 5px 0;
}
/* 隔行变色样式 */
.el-tree>.el-tree-node:nth-child(odd) {
  background: #f0f9eb;
}

.el-tree .el-tree-node .el-tree-node:nth-child(odd) {
  background: #fff;
}
</style>
