<template>
  <div class="home">欢迎访问QMS问题发布管理系统。</div>
</template>

<script lang='ts'>
import { Vue, Component } from "vue-property-decorator";

@Component({
  name: "Home",
})
class Home extends Vue {
  constructor(parameters) {
    super(parameters);
  }
}
export default Home;
</script>

<style lang="less" scoped>
.home {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
}
</style>
