<template>
  <el-row>
    <el-col :span="24">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>当前位置：权限组</el-breadcrumb-item>
        <el-breadcrumb-item>权限组列表</el-breadcrumb-item>
      </el-breadcrumb>
    </el-col>
    <el-col :span="24">
      <el-row :gutter="10">
        <el-form :model="params" :inline="false" size="small">
          <el-col :span="6">
            <el-form-item label="权限组：">
              <el-input clearable @change="qryList" maxlength="50" type="input" v-model="params.groupName"
                placeholder="请输入权限组权限组..."></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实验室类别：">
              <el-input clearable @change="qryList" maxlength="50" type="input" v-model="params.labName"
                placeholder="请输入权限组实验室类别..."></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU ID：">
              <el-input clearable @change="qryList" maxlength="50" type="input" v-model="params.productId"
                placeholder="请输入权限组SKU ID..."></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品名称：">
              <el-input clearable @change="qryList" maxlength="50" type="input" v-model="params.productName"
                placeholder="请输入权限组商品名称..."></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </el-col>
    <el-col :span="24" style="display: flex; justify-content: space-between;">
      <el-button type="primary" @click="handleAdd" size="small">新增权限组</el-button>
      <el-button type="primary" @click="qryList" size="small">搜索</el-button>
    </el-col>
    <el-col :span="24">
      <div class="group-list">
        <div class="group-head">
          <div class="group-name">权限组</div>
          <div class="group-shop">店铺</div>
          <div class="group-lab">实验室类别</div>
          <div class="group-lab">实验室实例</div>
          <div class="group-product">商品</div>
          <div class="group-action">操作</div>
        </div>
        <div class="group-body" v-if='datas.length'>
          <ul>
            <li v-for='(item, index) of datas' :key='index' :style="{ 'height': !item.isActive ? '50px' : 'auto' }">
              <div class="group-name">
                <span @click="handleRow(index)" style="color: #409EFF; padding-right: 10px;">{{ !item.isActive ? "展开" :
      "收起" }}</span>
                {{ item.groupName }}
              </div>
              <div class="group-items">
                <div class="group-item">
                  <div class="group-shop">
                    <span v-for='(prd, prdIndex) of item.shopNames' :key='prdIndex'>{{ prd }}</span></div>
                  <div class="group-lab">
                    <span v-for='(prd, prdIndex) of item.categoryNames' :key='prdIndex'>{{ prd }}</span></div>
                  <div class="group-lab">
                    <span v-for='(prd, prdIndex) of item.labNames' :key='prdIndex'>{{ prd }}</span>
                  </div>
                  <div class="group-product"><span v-for='(prd, prdIndex) of item.productNames' :key='prdIndex'>{{ prd }}</span></div> 
                </div>
              </div>
              <div class="group-action">
                <el-button type="primary" size='small' @click="handleEidt(item)">编辑</el-button>
                <el-button size='small' @click="handleDel(item.groupId)">删除</el-button>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="group-empty">暂无数据</div>
      </div>
    </el-col>
    <el-col :span="24">
      <div class="pagination">
        <el-pagination background layout="sizes, prev, pager, next" :page-sizes="[10, 50, 100]" :total="paginationTotal"
          @size-change="handleSizeChange" @current-change="handlePageChange"></el-pagination>
      </div>
    </el-col>
    <el-dialog title="权限组新增/编辑" :visible.sync="dialog.visible" width="70%" :close-on-click-modal='closeFlag'
      :show-close='closeFlag' :close-on-press-escape="closeFlag">
      <el-form ref="form" :model="form" label-width="110px" :rules="rules">
        <el-form-item label="名称：" prop="groupName">
          <el-input maxlength="50" type="input" v-model="form.groupName" placeholder="请输入权限组名称..."></el-input>
        </el-form-item>
        <el-form-item label="店铺/商品：" prop="lstDtl">
          <el-tree ref='refTree' :data="tree.datas.treeDatas" :props="tree.defaultProps" :node-key="tree.nodeKey"
            @check='handleCheck' :default-checked-keys="tree.datas.defaultCheckedKeys" show-checkbox>
          </el-tree>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancle">取消</el-button>
        <el-button @click="handleConfirm" type="primary">确认</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
  groupQryList,
  groupAdd,
  groupMod,
  groupDel,
  groupShopQry,
} from '../../api/common'
import { createTreeData } from './../../assets/javascript/util'

@Component({
  name: 'GroupList',
})
class GroupList extends Vue {
  private closeFlag = false
  private paginationTotal = 0
  private system = 'ticCenter'
  private datas = [{}] as any
  private shops = []
  // 查询入参
  private params = {
    shopId: '',
    productId: '',
    pageNum: 1,
    pageRow: 10,
  } as any
  private dialog = {
    visible: false,
  }
  // 添加和修改的入参
  private form = {
    lstDtl: [],
    groupName: '',
    groupId: 0,
  }
  // 必填校验
  private rules = {
    groupName: [
      { required: true, message: '请输入权限组名称', trigger: 'blur' },
    ],
    lstDtl: [{ required: true, message: '请选择店铺/商品', trigger: 'blur' }],
  }
  private tree = {
    nodeKey: 'releaveId', // 获取选中id对应的字段名
    defaultProps: {
      children: 'children', // 叶子节点的名称
      label: 'businessName', // 显示到页面的字段名
    },
    datas: {
      // 完整数据
      treeDatas: [],
      // 选中的数据
      defaultCheckedKeys: [],
    },
  }
  private productList = [] // 原始打平的产品列表

  // 切换分页
  handlePageChange(page: number): void {
    this.params.pageNum = page
    this.qryList()
  }

  // 切换每页条数
  handleSizeChange(size: number): void {
    this.params.pageRow = size
    this.qryList()
  }

  // 获取列表
  qryList(): void {
    groupQryList(this.params).then((res) => {
      // 实验室名称显示并去重
      res.items.forEach(v => {
        v.isActive = false
        // if (v.lstProduct.length) {
        //   v.lstProduct.forEach(v1 => {
        //     v1.labNames = []
        //     if (v1.lstProduct.length) {
        //       v1.lstProduct.forEach(v2 => {
        //         if (!v1.labNames.includes(v2.labName) && v2.labName) v1.labNames.push(v2.labName)
        //       })
        //     }
        //   })
        // }
      })
      console.log(res.items)
      this.datas = res.items
      this.paginationTotal = res.totalNum
    })
  }

  // 查询店铺列表
  qryShop(): void {
    groupShopQry({}).then((res) => {
      /* 创建ins数据源 */
      const ins = [
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 9999,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER",
          "businessName": "CRS Inspection",
          "parentCode": "-1",
          "type": "SHOP"
        },
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 1,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER_1",
          "businessName": "轻工产品",
          "parentCode": "INS_CUSTOMER",
          "type": "PRODUCT"
        },
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 2,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER_2",
          "businessName": "纺织品",
          "parentCode": "INS_CUSTOMER",
          "type": "PRODUCT"
        },
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 3,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER_3",
          "businessName": "电子电器",
          "parentCode": "INS_CUSTOMER",
          "type": "PRODUCT"
        }
      ]
      const shopList = [...res, ...ins]
      shopList.forEach((v: any) => {
        /* 重命名 */
        if (v.type === "LAB") v.businessName += "(实验室实例)"
        if (v.type === "CATEGORY") v.businessName += "(实验室类别)"
        // 将根节点的数据变为空
        v.parentId = v.parentCode !== '-1' ? v.parentCode : 0
        // 创建关联关系
        v.releaveId = v.businessCode
      })
      this.tree.datas.treeDatas = createTreeData(shopList, 'releaveId', 'parentId')
      console.log('this.tree.datas.treeDatas', this.tree.datas.treeDatas)
      this.productList = shopList
    })
  }

  // 选择店铺和商品
  handleCheck() {
    const refTree: any = this.$refs['refTree']
    const completeNodes = refTree.getCheckedNodes()
    // 过滤掉根节点和实验室
    this.form.lstDtl = completeNodes.filter((v) => v.type === "PRODUCT")
  }

  // 编辑权限组
  handleEidt(row: any): void {
    this.dialog.visible = true
    this.form.groupId = row.groupId
    this.form.groupName = row.groupName
    this.tree.datas.defaultCheckedKeys = row.businessCodes
    // 编辑时，赋值选中之后在执行一次选中，可以触发送参数生成
    setTimeout(() => {
      this.handleCheck()
    }, 1)
  }

  // 删除权限组
  handleDel(groupId: number): void {
    this['$confirm'](`您确认删除？`).then(() => {
      groupDel({ groupId }).then((res) => {
        this.qryList()
      })
    })
  }

  // 添加权限组
  handleAdd() {
    this.dialog.visible = true
  }

  // 关闭权限组弹窗
  handleCancle() {
    this.closeDialog()
  }

  // 确认权限组弹窗
  handleConfirm() {
    const form: any = this.$refs['form']
    form.validate((valid) => {
      if (valid) {
        if (this.form.groupId) {
          groupMod(this.form).then((res) => {
            if (res) {
              this['$message'].success('修改成功！')
              this.closeDialog()
            }
          })
        } else {
          delete this.form.groupId
          groupAdd(this.form).then((res) => {
            if (res) {
              this['$message'].success('添加成功！')
              this.closeDialog()
            }
          })
        }
      }
    })
  }

  // 关闭整体处理业务
  closeDialog() {
    const refTree: any = this.$refs['refTree']
    refTree.setCheckedKeys([])
    this.tree.datas.defaultCheckedKeys = []
    this.dialog.visible = false
    this.form = {
      lstDtl: [],
      groupName: '',
      groupId: 0,
    }
    this.qryList()
  }

  // 切换行高显示
  handleRow(index: number) {
    this.datas[index].isActive = !this.datas[index].isActive
  }

  mounted() {
    this.qryList()
    this.qryShop()
  }
}
export default GroupList
</script>

<style lang="less" scoped>
.group-list {
  margin-top: 10px;
  background: #fff;
  font-size: 12px;
  color: #666;
  border-radius: 3px;

  .group-head {
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #ddd;

    div {
      font-weight: bold;
      padding: 0 10px;
      box-sizing: border-box;
      text-align: left;

      &:nth-child(5) {
        flex: 3;
      }
    }
  }

  .group-body {
    li {
      padding: 5px 0;
      display: flex;
      justify-content: flex-start;
      border-bottom: 1px solid #ddd;
      overflow: hidden;

      &:nth-child(odd) {
        background: #f5f5f5;
      }

      &>div {
        padding-left: 10px;
      }

      div {
        box-sizing: border-box;
        text-align: left;

        &.group-items {
          flex: 1;
        }

        .group-item {
          display: flex;
          flex-direction: inherit;
          // padding: 5px 0;
          // height: 25px;
          line-height: 25px;
        }
      }
    }

    .group-product {
      flex: 1;
      flex-direction: column;
      display: flex;
    }
  }

  .group-action {
    width: 150px;
    // border-left: 1px solid #ddd;
  }

  .group-name {
    width: 150px;
    // border-right: 1px solid #ddd;

    span {
      cursor: pointer;
    }
  }

  .group-lab {
    width: 150px;
    margin-right: 10px;
    // border-right: 1px solid #ddd;
  }

  .group-shop {
    width: 200px;
    // align-items: center;
    display: flex;
    flex-direction: column;
    // border-right: 1px solid #ddd;
  }

  .group-item {
    // border-bottom: 1px solid #ddd;

    &:last-child {
      border: none;
    }

    span {
      padding: 1px 0;
    }
  }

  .group-empty {
    text-align: center;
    line-height: 50px;
    height: 50px;
  }
}
</style>