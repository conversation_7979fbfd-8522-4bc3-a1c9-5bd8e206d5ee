<template>
  <el-row :gutter="10">
    <el-col :span="24">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>当前位置：邮件组</el-breadcrumb-item>
        <el-breadcrumb-item>邮件组列表</el-breadcrumb-item>
      </el-breadcrumb>
    </el-col>
    <el-form ref="form" :model="qryItemParam" :inline="false" size="small">
      <el-col :span="6">
        <el-form-item label="邮件组">
          <el-input placeholder="请输入邮件组..." type="text" v-model="qryItemParam.groupNo" clearable @change="handleSearch">
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="邮件地址">
          <el-input placeholder="请输入邮件地址..." type="text" v-model="qryItemParam.groupMail" clearable
            @change="handleSearch"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="商品">
          <el-input placeholder="请输入商品..." type="text" v-model="qryItemParam.productName" clearable
            @change="handleSearch"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="邮件模板名称">
          <el-input placeholder="请输入邮件模板名称..." type="text" v-model="qryItemParam.templateName" clearable
            @change="handleSearch"></el-input>
        </el-form-item>
      </el-col>
    </el-form>
    <el-col :span="12" style="text-align: left;">
      <el-button type="primary" @click="handleAdd" size="small">新增邮件组</el-button>
    </el-col>
    <el-col :span="12" style="text-align: right;">
      <el-button type="primary" @click="handleSearch" size="small">搜索</el-button>
    </el-col>
    <el-col :span="24" style="margin-top: 10px;">
      <el-table :data="datas" style="width: 100%">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" class="demo-table-expand">
              <el-form-item label="邮件模板：">
                <el-tag v-for="(item, index) in props.row.templateName" :key='index' size="small" type="success">{{ item
                }}</el-tag>
              </el-form-item>
              <el-form-item label="详细描述：">
                <span>{{ props.row.groupName }}</span>
              </el-form-item>
              <el-form-item label="店铺/商品：">
                <el-tag v-for="(item, index) in props.row.productName" :key='index' size="small" type="info">{{ item
                }}</el-tag>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="邮件组名称" prop="groupNo" width="150">
        </el-table-column>
        <el-table-column label="类别" prop="mailType" width="50" align="center">
          <template slot-scope="scope">
            {{ scope.row.mailType === 2 ? '售前' : '售后' }}
          </template>
        </el-table-column>
        <el-table-column label="TO" prop="groupMail">
          <template slot-scope="scope">
            <el-tag v-for="(to, index) in scope.row.groupMail" :key='index' size="small">{{ to }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align='center'>
          <template slot-scope="scope">
            <el-button type="primary" size='small' @click="handleEidt(scope.row.groupId)">编辑</el-button>
            <el-button size='small' @click="handleDel(scope.row.groupId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <!-- <el-col :span="24">
      <el-table :data="datas" stripe size='small'>
        <el-table-column label="邮件组" width="150" prop="groupNo"></el-table-column>
        <el-table-column label="描述" prop="groupName"></el-table-column>
        <el-table-column label="TO" width="150" prop="groupMail">
          <template slot-scope="scope">
            <div v-for='(item, index) of scope.row.groupMail' :key='index'>{{ item }}</div>
          </template>
        </el-table-column>
        <el-table-column label="商品" width="200" prop="productName">
          <template slot-scope="scope">
            <div v-for='(item, index) of scope.row.productName' :key='index'>{{ item }}</div>
          </template>
        </el-table-column>
        <el-table-column label="邮件模板" width="200" prop="templateName">
          <template slot-scope="scope">
            <div v-for='(item, index) of scope.row.templateName' :key='index'>{{ item }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align='center'>
          <template slot-scope="scope">
            <el-button type="primary" size='small' @click="handleEidt(scope.row.groupId)">编辑</el-button>
            <el-button size='small' @click="handleDel(scope.row.groupId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-col> -->
    <el-col :span="24">
      <div class="pagination">
        <el-pagination background layout="sizes, prev, pager, next" :page-sizes="[10, 50, 100]" :total="paginationTotal"
          @size-change="handleSizeChange" @current-change="handlePageChange"></el-pagination>
      </div>
    </el-col>
    <el-dialog title="邮件组新增/编辑" :visible.sync="dialog.visible" :show-close='closeFlag' width="70%"
      :close-on-click-modal='closeFlag' :close-on-press-escape="closeFlag" v-loading="loading">
      <el-form ref="form" :model="addItemParam" label-width="110px" :rules="rules">
        <el-form-item label="邮件组名称：" prop="groupNo">
          <el-input maxlength="50" type="input" v-model="addItemParam.groupNo" placeholder="请输入邮件组名称..."></el-input>
        </el-form-item>
        <el-form-item label="描述：" prop="groupName">
          <el-input type='textarea' maxlength="50" v-model="addItemParam.groupName" placeholder="请输入描述..."></el-input>
        </el-form-item>
        <el-form-item label="TO：" prop="groupMail">
          <el-select style="width: 100%" v-model="groupMails" multiple filterable remote reserve-keyword
            placeholder="请输入邮件地址..." :remote-method="personRemote" :loading="loading">
            <el-option v-for="(item, index) in personOptions" :key="index" :value="item.personMail">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="店铺/商品：" prop="productItem">
          <el-tree ref='refTree' :data="tree.datas.treeDatas" :props="tree.defaultProps" :node-key="tree.nodeKey"
            @check='handleCheck' :default-checked-keys="tree.datas.defaultCheckedKeys" show-checkbox>
          </el-tree>
        </el-form-item>
        <el-form-item label="类别：" prop="mailType">
          <el-radio-group v-model="addItemParam.mailType" @change="handleChangeMailType">
            <el-radio label="2" value="2">售前</el-radio>
            <el-radio label="3" value="3">售后</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="邮件模板：" prop="templateItem">
          <!-- <el-select style="width: 100%" v-model="templateItems" multiple filterable remote reserve-keyword
            placeholder="请输入邮件模板..." :remote-method="mailRemote" :loading="loading">
            <el-option v-for="(item, index) in mailOptions" :disabled='item.disabled' :key="index"
              :value="item.templateId" :label="item.templateName">
              {{ item.templateId }}：{{ item.templateName }}（{{ item.buType }}）
            </el-option>
          </el-select> -->
          <el-select style="width: 100%" v-model="templateItems" placeholder="请输入邮件模板..." multiple filterable>
            <el-option v-for="(item, index) in mailTemplateList" :disabled='item.disabled' :key="index"
              :value="item.templateId" :label="item.templateName">
              {{ item.templateId }}：{{ item.templateName }}（{{ item.buType }}）
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancle">取消</el-button>
        <el-button @click="handleConfirm" type="primary">确认</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import {
  mailGroupQryList,
  mailGroupAdd,
  mailGroupDel,
  groupShopQry,
  mailTemplateQry,
  personQry,
  mailTemplateQryDtl,
} from '../../api/common'
import { createTreeData } from './../../assets/javascript/util'

@Component({
  name: 'EmailGroupList',
})
class EmailGroupList extends Vue {
  private closeFlag = false
  private paginationTotal = 0
  private system = 'ticCenter'
  private datas = [{}]
  private shops = []
  private dialog = {
    visible: false,
  }
  // 必填校验
  private rules = {
    groupNo: [{ required: true, message: '请输入邮件组名称', trigger: 'blur' }],
  }
  private tree = {
    nodeKey: 'releaveId', // 获取选中id对应的字段名
    defaultProps: {
      children: 'children', // 叶子节点的名称
      label: 'businessName', // 显示到页面的字段名
    },
    datas: {
      // 完整数据
      treeDatas: [],
      // 选中的数据
      defaultCheckedKeys: [],
    },
  }

  // 查询入参
  private qryItemParam = {
    productName: '',
    templateName: '',
    groupNo: '',
    groupMail: '',
    pageNum: 1,
    pageRow: 10,
  } as any
  // 邮件模板列表
  private mailTemplateList = []
  // 模板下拉数据
  private mailOptions = []
  // 发送人邮箱列表
  private personList = []
  // 发送人下拉数据
  private personOptions = []
  // 添加邮件模板入参
  private addItemParam = {
    groupId: 0,
    groupName: '',
    groupNo: '',
    groupMail: '',
    productItem: [],
    templateItem: [],
    mailType: '3'
  } as any
  // 查询邮件模板入参
  private qryMailTemplate = {
    pageRow: 9999,
    pageNum: 1,
    mailType: 3,
    buItem: ['ALL']
  }
  private templateItems = [] as any
  private groupMails = []
  private loading = false
  private productList = [] // 原始打平的产品列表

  // 切换分页
  handlePageChange(page: number): void {
    this.qryItemParam.pageNum = page
    this.qryList()
  }

  // 切换每页条数
  handleSizeChange(size: number): void {
    this.qryItemParam.pageRow = size
    this.qryList()
  }

  // 静态查询模板组
  mailRemote(query): void {
    if (query) {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.mailOptions = this.mailTemplateList.filter((item) => {
          return (
            item.templateName.toLowerCase().indexOf(query.toLowerCase()) > -1
          )
        })
      }, 200)
    } else {
      this.mailOptions = this.mailTemplateList
    }
  }

  // 静态查询用户邮箱
  personRemote(query): void {
    if (query) {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.personOptions = this.personList.filter((item) => {
          return item.personMail.toLowerCase().indexOf(query.toLowerCase()) > -1
        })
      }, 200)
    } else {
      this.personOptions = this.personList
    }
  }

  handleSearch(): void {
    this.qryList()
  }

  // 获取邮件组列表
  qryList(): void {
    mailGroupQryList(this.qryItemParam).then((res) => {
      // fix bug TIC-CENTER-WEB-1
      if (res.items) {
        res.items.map((v) => {
          v.productName = v.productName.split(',')
          v.templateName = v.templateName.split(',')
          v.groupMail = v.groupMail.split(',')
        })
        this.datas = res.items
        this.paginationTotal = res.totalNum
      }
    })
  }

  // 获取邮件模板列表
  handleChangeMailType() {
    this.qryEmailTempalte()
  }
  qryEmailTempalte() {
    this.qryMailTemplate.mailType = this.addItemParam.mailType
    mailTemplateQry(this.qryMailTemplate).then((res) => {
      res.forEach((v) => {
        if (v.mailType === 1) {
          v.disabled = true
        } else {
          v.disabled = false
        }
      })
      this.mailTemplateList = res
      // this.templateItems = []
    })
  }

  // 查询全部bu
  qryBuList(): void {
    personQry({ pageNum: 1, pageRow: 9999, personMail: '' }).then((res) => {
      this.personList = res.filter((v) => v.personMail)
    })
  }

  // 查询店铺列表
  qryShop(): void {
    groupShopQry({}).then((res) => {
      /* 创建ins数据源 */
      const ins = [
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 9999,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER",
          "businessName": "CRS Inspection",
          "parentCode": "-1",
          "type": "SHOP"
        },
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 1,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER_1",
          "businessName": "轻工产品",
          "parentCode": "INS_CUSTOMER",
          "type": "PRODUCT"
        },
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 2,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER_2",
          "businessName": "纺织品",
          "parentCode": "INS_CUSTOMER",
          "type": "PRODUCT"
        },
        {
          "shopId": 9999,
          "shopCode": "INS_CUSTOMER",
          "shopName": "CRS Inspection",
          "productId": 3,
          "labId": 9999,
          "categoryId": 9999,
          "businessId": 9999,
          "businessCode": "INS_CUSTOMER_3",
          "businessName": "电子电器",
          "parentCode": "INS_CUSTOMER",
          "type": "PRODUCT"
        }
      ]
      const shopList = [...res, ...ins]
      shopList.forEach((v: any) => {
        /* 重命名 */
        if (v.type === "LAB") v.businessName += "(实验室实例)"
        if (v.type === "CATEGORY") v.businessName += "(实验室类别)"
        // 将根节点的数据变为空
        v.parentId = v.parentCode !== '-1' ? v.parentCode : 0
        // 创建关联关系
        v.releaveId = v.businessCode
      })
      this.tree.datas.treeDatas = createTreeData(shopList, 'releaveId', 'parentId')
      this.productList = shopList
    })
  }

  // 选择店铺和商品
  handleCheck() {
    const refTree: any = this.$refs['refTree']
    const completeNodes = refTree.getCheckedNodes(false, true)
    // 过滤掉根节点和实验室
    this.addItemParam.productItem = completeNodes.filter((v) => v.type === "PRODUCT")
    console.log('this.addItemParam.productItem', this.addItemParam.productItem)
    // 获取出根节点，并生成buType
    this.qryMailTemplate.buItem = []
    this.qryMailTemplate.buItem = completeNodes.filter((v) => !v.parentId).map((v1) => v1.shopCode)
    this.qryMailTemplate.buItem.push('ALL')
    this.qryEmailTempalte()
  }

  // 编辑邮件组
  handleEidt(groupId: any): void {
    this.loading = true
    this.dialog.visible = true
    mailTemplateQryDtl({ groupId }).then((res) => {
      this.addItemParam.groupId = res.groupId
      this.addItemParam.groupName = res.groupName
      this.addItemParam.groupNo = res.groupNo
      this.addItemParam.groupMail = res.groupMail
      this.addItemParam.mailType = res.mailType && String(res.mailType)
      // 选中默认值
      res.templateIds.split(',').forEach((v) => {
        this.templateItems.push(Number(v))
      })
      this.mailOptions = this.mailTemplateList
      this.groupMails = res.groupMail.split(',')
      // let producsList = [...new Set(res.productIds.split(','))]
      // let checkList = []
      // 根据原始数据以及用户选中的产品列表，生成新的选中列表
      // producsList.forEach((v1) => {
      //   this.productList.forEach(v2 => {
      //     if (v1 == v2.productId) checkList.push(v2.releaveId)
      //   })
      // })
      setTimeout(() => {
        this.tree.datas.defaultCheckedKeys = res.businessCodes
        debugger
      }, 100)
      setTimeout(() => {
        this.handleCheck()
        this.qryEmailTempalte()
        this.loading = false
      }, 1000)
    })
  }

  // 删除邮件组
  handleDel(groupId: number): void {
    this['$confirm'](`您确认删除？`).then(() => {
      mailGroupDel({ groupId }).then((res) => {
        this.qryList()
      })
    })
  }

  // 添加邮件组
  handleAdd() {
    this.dialog.visible = true
  }

  // 关闭邮件组弹窗
  handleCancle() {
    this.closeDialog()
  }

  // 确认邮件组弹窗
  handleConfirm() {
    // 生成数数据
    this.addItemParam.templateItem = []
    this.mailTemplateList.forEach((v) => {
      if (this.templateItems.includes(v.templateId)) {
        this.addItemParam.templateItem.push({
          businessType: v.businessType,
          mailType: v.mailType,
          templateId: v.templateId,
          buType: v.buType,
        })
      }
    })
    this.addItemParam.groupMail = this.groupMails.join(',')

    const form: any = this.$refs['form']
    form.validate((valid) => {
      if (valid) {
        if (this.addItemParam.groupId) {
          mailGroupAdd(this.addItemParam).then((res) => {
            if (res) {
              this['$message'].success('修改成功！')
              this.closeDialog()
            }
          })
        } else {
          delete this.addItemParam.groupId
          mailGroupAdd(this.addItemParam).then((res) => {
            if (res) {
              this['$message'].success('添加成功！')
              this.closeDialog()
            }
          })
        }
      }
    })
  }

  // 关闭整体处理业务
  closeDialog() {
    const refTree: any = this.$refs['refTree']
    refTree.setCheckedKeys([])
    this.tree.datas.defaultCheckedKeys = []
    this.templateItems = []
    this.groupMails = []

    this.dialog.visible = false
    this.addItemParam = {
      groupId: 0,
      groupName: '',
      groupNo: '',
      groupMail: '',
      productItem: [],
      templateItem: [],
      mailType: '3'
    }
    this.qryList()
  }

  mounted() {
    this.qryList()
    this.qryShop()
    this.qryBuList()
  }
}
export default EmailGroupList
</script>

<style lang="less" scoped>
.el-tag {
  margin: 3px;
}

.demo-table-expand {
  font-size: 0;
  margin: 0 30px;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
}
</style>