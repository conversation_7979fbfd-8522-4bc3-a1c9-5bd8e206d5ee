import Vue from 'vue';
import Vuex from 'vuex';
import apis from './apis'
Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    qsListRefresh: false,
    testItemRefresh: false,
    qryBu: {
      refresh: false
    },
    account: {
      visible: false
    },
    laboratory: {
      visible: false,
      data: {},
      businessLineRadioProps: {}
    },
    userRights: null,
    subBu: {
      visible: false,
      data: {}
    },
    renameBu: {
      visible: false,
      data: {}
    },
    label: {
        visible: false,
        data: {},
      },
  },
  mutations: {
    setQsListRefresh(state, value) {
      state.qsListRefresh = value
    },
    setTestItemRefresh(state, value) {
      state.testItemRefresh = value
    },
    setQryBu(state, value) {
      state.qryBu = value
    },
    setAccount(state, value) {
      state.account = value
    },
    setLaboratory(state, value) {
      state.laboratory = value
    },
    setLabel(state, value) {
        state.label = value
      },
    setUserRights(state, value) {
      state.userRights = value
    },
    setSubBu(state, value) {
      state.subBu = value
    },
    setRenameBu(state, value) {
      state.renameBu = value
    }
  },
  actions: {
    async ['getUserRights']({
      commit
    }, params = {}) {
      const res = await apis.getUserRights(params);
      commit('setUserRights', res.data);
      return res.data || {};
    },
  },
});