import apis from './apis.js'

export default {
  namespaced: true,
  state: {
    //app版本
    appVersionListState: {
      action: "getAppVersionList",
      params: {},
      data: [],
    },
    //app内容
    appContentListState: {
      action: "getAppContentList",
      params: {},
      data: [],
    },
  },
  actions: {
    //app版本
    async ['getAppVersionList']({commit}, params = {}) {
      const res = await apis.getAppVersionList(params);
      commit("appVersionList", res.data.data ? res.data.data.recordList || [] : []);
      return res.data || {};
    },
    //app内容
    async ['getAppContentList']({commit}, params = {}) {
      const res = await apis.getAppContentList(params);
      commit("appContentList", res.data.data ? res.data.data.recordList || [] : []);
      return res.data || {};
    },
  },
  mutations: {
    //app版本
    appVersionList(state, data) {
      state.appVersionListState.data = data;
    },
    //app内容
    appContentList(state, data) {
      state.appContentListState.data = data;
    },
  },
}
