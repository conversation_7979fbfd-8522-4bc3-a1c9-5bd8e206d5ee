import {ajax} from '@/assets/javascript/util'

export default {
//获取app版本列表
  getAppVersionList(params) {
    return ajax(params, '/appVersion/list', "get")
  },
  //获取单条app版本记录
  getAppVersion(params) {
    return ajax(params, '/appVersion/get', "get")
  },
  //添加一条app版本记录
  addAppVersion(params) {
    return ajax(params, '/appVersion/add')
  },
  //更新一条app版本记录
  updateAppVersion(params) {
    return ajax(params, '/appVersion/update')
  },
  //删除一条app版本记录
  deleteAppVersion(params) {
    return ajax(params, '/appVersion/delete')
  },
  //更新app版本的IOS/安卓发布状态
  updateAppInlineStatus(params) {
    return ajax(params, '/appVersion/clientOnline')
  },
  // 获取app内容列表
  getAppContentList(params) {
    return ajax(params, '/appContent/list', "get")
  },
  //获取单条app内容记录
  getAppContent(params) {
    return ajax(params, '/appContent/get', "get")
  },
  //添加单条app内容记录
  addAppContent(params) {
    return ajax(params, '/appContent/add')
  },
  //更新单条app内容记录
  updateAppContent(params) {
    return ajax(params, '/appContent/update')
  },
  //删除单条app内容记录
  deleteAppContent(params) {
    return ajax(params, '/appContent/delete')
  },
  //更新app内容IOS/安卓发布状态
  updateAppContentStatus(params) {
    return ajax(params, '/appContent/changeState')
  },
}
