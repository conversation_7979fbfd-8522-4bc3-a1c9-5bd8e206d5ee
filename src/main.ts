import Vue from 'vue'
import VueRouter from 'vue-router'
import App from './App.vue'
import createRouter from './router/router.js'
import store from './store'
import './plugins/element.js'
import './assets/style/common.less'
import './assets/style/reset.less'
// import ticComponentsVue2 from 'tic-components-vue2'
import { monitorInit } from '@tic/web-monitor'
monitorInit({ env: process.env.NODE_ENV, system: 'centerWeb' })
/* sentry init */
import * as Sentry from "@sentry/vue";
import { BrowserTracing } from "@sentry/tracing";
const packageFile = require('./../package.json')

Vue.config.productionTip = false
Vue.use(VueRouter)
// Vue.use(ticComponentsVue2)

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => {
    return err
  })
}

// 全局指令（按钮权限）
Vue.directive('btnAuth', {
  bind: (el, binding): void => {
    const btns = store.state.userRights.funcItem.filter(v => {
      return v.funcType === 2
    })
    if (!btns.some(v => {
      return v.buttonCode == binding.value
    })) {
      el.setAttribute('disabled', true) // 没有权限禁用按钮
      el.style.display = 'none' // 没有权限隐藏按钮
    }
  },
  // inserted: (): void => {
  // },
  // update: (): void => {
  // },
  // unbind: (): void => {
  // },
})

const router = createRouter()
// 生产环境开启sentry监控
console.log('process.env.NODE_ENV', process.env.NODE_ENV)
// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
process.env.NODE_ENV === 'product' || process.env.NODE_ENV === 'azureprod'  && Sentry.init({
  Vue,
  dsn: "https://<EMAIL>/30",
  release: packageFile.version,
  integrations: [
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    new BrowserTracing({
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      routingInstrumentation: Sentry.vueRouterInstrumentation(router),
      tracingOrigins: ["localhost", "my-site-url.com", /^\//],
    }),
  ],
  tracesSampleRate: 1.0,
});

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

// import { registerMicroApps, start } from 'qiankun';
// // 注册子应用
// registerMicroApps([
//   {
//     name: 'centerWeb-vue3',
//     // entry: '//localhost:9013',
//     entry: '//localhost:8082',
//     container: '#centerWeb-vue3',
//     activeRule: '/child/child1',         // 子应用触发规则（路径）
//   },
// ]);
// // 开启服务
// start()
