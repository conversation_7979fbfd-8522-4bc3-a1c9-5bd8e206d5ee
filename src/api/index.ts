import axios from 'axios'
import md5 from 'crypto-md5'
import {
  local,
  redirect,
  apiHost,
  CurrentSystem,
  version
} from './../assets/javascript/util'
import { apiErrorWatch } from './../assets/javascript/util'

axios.defaults.timeout = 600000; //10分钟后超时
axios.interceptors.request.use(config => {
  return config
}, err => {
  //请求出错的处理函数
  return Promise.reject(err)
})

const ajaxNew = async (url: string, params: object, method, option?) => {
  let host = '',
    pid = '',
    pcode = ''
  if (!option || !option.type) {
    host = apiHost + 'ticCenter'
    pid = 'pid.center'
    pcode = 'RK0O1W3C4uVU1UHa'
  } else if (option.type === 'ticSend') {
    host = apiHost + option.type
    pid = 'pid.center'
    pcode = 'RK0O1W3C4uVU1UHa'
  } else if (option.type === 'ticSso') {
    host = apiHost + option.type
    pid = 'pid.leads'
    pcode = 'saUqq8AHsS7kQH8s'
  } else if (option.type === 'solrServer') {
    host = apiHost + option.type
    pid = 'pid.order'
    pcode = 'kPhG6so9xOHY3QsO'
  } else if (option.type === 'ticOrder') {
    host = apiHost + option.type
    pid = 'pid.order'
    pcode = 'kPhG6so9xOHY3QsO'
  } else if (option.type === 'ticPayment') {
    host = apiHost + option.type
    pid = 'pid.center'
    pcode = 'RK0O1W3C4uVU1UHa'
  } 

  const timestamp = new Date().valueOf()
  const pmd5 = md5((pid + pcode).toUpperCase(), 'hex');
  const param = JSON.stringify(params) + pmd5.toUpperCase();
  const sign = md5(param + timestamp, 'hex');

  let appId = ''
  let accessToken = ''
  if (local.get('token')) {
    appId = local.get('token').appId
    accessToken = local.get('token').accessToken
  }
  // 附加系统信息
  let system = ''
  if (option && option.system) {
    system = option.system
  } else {
    system = CurrentSystem
  }
  
  const headers = {
    'Content-Type': 'application/json',
    sign,
    timestamp,
    pid,
    appId,
    accessToken,
    system,
    version,
    frontUrl: window.location.href
  }
  for (const item in headers) {
    if (!headers[item]) delete headers[item]
  }

  try {
    const res = await axios({
      method: method || 'post',
      url: host + url,
      data: params,
      headers
    })
    if (res.data.resultCode == 9978) {
      local.clear()
      redirect()
    }
    else {
      return res.data
    }
  }
  catch (error) {
    apiErrorWatch(error, params, host + url)
    //token认证失败，重新登录
    if (error.response.status === 401) {
      local.clear()
      redirect()
      // 非法输入拦截
    }
    else if (error.response.status === 400) {
      return {
        resultCode: error.response.data.status,
        resultMsg: error.response.data.message
      }
    }
    else {
      return error
    }
  }
}

const ajaxExport = async (url: string, params: object, method, option?) => {
  let host = '',
    pid = '',
    pcode = ''
  if (!option) {
    host = apiHost + 'ticCenter'
    pid = 'pid.center'
    pcode = 'RK0O1W3C4uVU1UHa'
  } else if (option.type === 'ticSend') {
    host = apiHost + option.type
    pid = 'pid.center'
    pcode = 'RK0O1W3C4uVU1UHa'
  } else if (option.type === 'ticSso') {
    host = apiHost + option.type
    pid = 'pid.leads'
    pcode = 'saUqq8AHsS7kQH8s'
  } else if (option.type === 'solrServer') {
    host = apiHost + option.type
    pid = 'pid.leads'
    pcode = 'saUqq8AHsS7kQH8s'
  } else if (option.type === 'ticOrder') {
    host = apiHost + option.type
    pid = 'pid.order'
    pcode = 'kPhG6so9xOHY3QsO'
  } else if (option.type === 'ticPayment') {
    host = apiHost + option.type
    pid = 'pid.center'
    pcode = 'RK0O1W3C4uVU1UHa'
  }

  const timestamp = new Date().valueOf()
  const pmd5 = md5((pid + pcode).toUpperCase(), 'hex');
  const param = JSON.stringify(params) + pmd5.toUpperCase();
  const sign = md5(param + timestamp, 'hex');

  let appId = ''
  let accessToken = ''
  if (local.get('token')) {
    appId = local.get('token').appId
    accessToken = local.get('token').accessToken
  }

  const headers = {
    'Content-Type': 'application/json',
    sign,
    timestamp,
    pid,
    appId,
    accessToken,
    system: CurrentSystem
  }
  for (const item in headers) {
    if (!headers[item]) delete headers[item]
  }

  try {
    const res = await axios({
      method: method || 'post',
      url: host + url,
      data: params,
      responseType: "blob",
      headers
    })
    if (res.data.resultCode == 9978) {
      local.clear()
      redirect()
    }
    else {
      return res
    }
  }
  catch (error) {
    apiErrorWatch(error, params, host + url)
    //token认证失败，重新登录
    if (error.response.status === 401) {
      local.clear()
      redirect()
      // 非法输入拦截
    }
    else if (error.response.status === 400) {
      return {
        resultCode: error.response.data.status,
        resultMsg: error.response.data.message
      }
    }
    else {
      return error
    }
  }
}

export {
  ajaxNew,
  ajaxExport
}
