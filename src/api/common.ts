import { ajaxNew } from "./index";
import { Message } from 'element-ui';

// 获取省市区
const addressQry = (parentOrgId) => {
    return ajaxNew("/business/api.v1.center/org/qry", { parentOrgId }, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 获取账户信息
const qryAccount = (params) => {
    return ajaxNew("/business/api.v1.account/account/qry", params, "post", {
        type: 'ticPayment'
    })
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

//  获取BU列表
const qryBU = params => {
    return ajaxNew("/business/api.v1.center/bu/qryBySystem", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 查询带有业务线和实验室的bu列表
/* 传递state 返回带有实验室，不传没有实验室信�?*/
const qryBuInfo = params => {
    return ajaxNew("/business/api.v1.center/bu/qryInfoByBu", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// BU重命名
const rnameBu = params => {
    return ajaxNew("/business/api.v1.center/bu/modSysBu", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 创建SubBu
const addSubBu = params => {
    return ajaxNew("/business/api.v1.center/bu/addSubBu", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 创建业务线
const addBusiness = params => {
    return ajaxNew("/business/api.v1.center/basic/add", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 修改业务线
const modBusiness = params => {
    return ajaxNew("/business/api.v1.center/basic/mod", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 删除业务线
const delBusiness = params => {
    return ajaxNew("/business/api.v1.center/basic/del", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 查询实验室（带bu）
const qryLaboratory = params => {
    return ajaxNew("/business/api.v1.center/lab/qry", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 创建实验室
const addLaboratory = params => {
    return ajaxNew("/business/api.v1.center/lab/add", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 修改实验室
const modLaboratory = params => {
    return ajaxNew("/business/api.v1.center/lab/mod", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                window.location.reload()
                return true;
            } else {
                Message.error(res.resultMsg);
                return false;
            }
        })
        .catch((e) => {
            Message.error(e);
            return false;
        });
}

// 获取实验室详情
const getLaboratory = params => {
    return ajaxNew("/business/api.v1.center/lab/qryDtl", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return {};
            }
        })
        .catch((e) => {
            Message.error(e);
            return {};
        });
}

// 根据BU查询业务线
const qryInfoByLab = params => {
    return ajaxNew("/business/api.v1.center/basic/qryLineByBu", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 获取全业务线(不带bu)
const qryBusiness = params => {
    return ajaxNew("/business/api.v1.center/basic/qry", params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 获取问题对应的数据（根据章节位置查询题目)
const qryByPositionAction = params => {
    params.qryType = 1
    return ajaxNew('/business/api.v1.question/qsubject/qryByPosition', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.subjectItem && res.data.subjectItem.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 根据渠道编码查询账户信息
const qryByChannel = params => {
    return ajaxNew('/business/api.v1.account/account/qryByChannel', params, "post", {
        type: 'ticPayment'
    })
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 获取单个枚举值
const qryEnum = params => {
    return ajaxNew('/business/api.v1.center/enum/get', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 获取批量枚举值
const qryEnumBrach = params => {
    return ajaxNew('/business/api.v1.account/account/qryByChannel', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 根据业务查询样品基础配置
const qryByBusi = params => {
    return ajaxNew('/business/api.v1.center/sample/qryByBusi', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data.items
            } else {
                Message.error(res.resultMsg);
            }
        })
        .catch((e) => {
            Message.error(e);
        });
}

// 根据业务新增样品基础配置
const modSample = params => {
    return ajaxNew('/business/api.v1.center/sample/add', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  复制高级规则
  http://10.168.129.139:3000/project/21/interface/api/3853
*/
const copyRelevanceGroup = params => {
    return ajaxNew('/business/api.v1.question/qitem/copy', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  查询权限组列表
  http://10.168.129.139:3000/project/21/interface/api/3933
*/
const groupQryList = params => {
    return ajaxNew('/business/api.v1.center/authgroup/qry', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
  新增权限组
  http://10.168.129.139:3000/project/21/interface/api/3935
*/
const groupAdd = params => {
    return ajaxNew('/business/api.v1.center/authgroup/add', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  修改权限组
  http://10.168.129.139:3000/project/21/interface/api/3937
*/
const groupMod = params => {
    return ajaxNew('/business/api.v1.center/authgroup/mod', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  删除权限组
 http://10.168.129.139:3000/project/21/interface/api/3939
*/
const groupDel = params => {
    return ajaxNew('/business/api.v1.center/authgroup/del', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  查询店铺商品
  http://10.168.129.139:3000/project/21/interface/api/3941
*/
const groupShopQry = params => {
    return ajaxNew('/business/api.v1.center/authgroup/qryProduct', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
  查询邮件组列表
  http://10.168.129.139:3000/project/21/interface/api/4003
*/
const mailGroupQryList = params => {
    return ajaxNew('/business/api.v1.center/mailgroup/qry', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
  保存邮件组
  http://10.168.129.139:3000/project/21/interface/api/4005
*/
const mailGroupAdd = params => {
    return ajaxNew('/business/api.v1.center/mailgroup/save', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  删除邮件组
  http://10.168.129.139:3000/project/21/interface/api/4019
*/
const mailGroupDel = params => {
    return ajaxNew('/business/api.v1.center/mailgroup/del', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  查询邮件组详情
*/
const mailTemplateQryDtl = params => {
    return ajaxNew('/business/api.v1.center/mailgroup/qryDtl', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return {}
            }
        })
        .catch((e) => {
            Message.error(e);
            return {}
        });
}

/*
  查询邮件模板列表
*/
const mailTemplateQry = params => {
    return ajaxNew('/business/api.v1.send/mailTemplate/qry', params, "post", {
        type: "ticSend",
    })
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
  查询全部用户数据
*/
const personQry = params => {
    return ajaxNew('/business/api.v1.solr/person/qryCs', params, "post", {
        type: "solrServer",
    })
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
  复制测试项目
  http://10.168.129.139:3000/project/21/interface/api/4059
*/
const itemCopy = params => {
    return ajaxNew('/business/api.v1.center/item/copy', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
  查询样品KEY
  http://10.168.136.67:3000/project/21/interface/api/4143
*/
const qrySampleKey = params => {
    return ajaxNew('/business/api.v1.center/samplekey/get', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/* 
    查询标签配置
    http://10.169.128.35:3000/project/21/interface/api/10753
*/
const qryTag = params => {
    return ajaxNew('/business/api.v1.question/qlabel/qryByCondition', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => { })
}
/* 
    新增标签配置
    http://10.169.128.35:3000/project/21/interface/api/10747
 */
const addTag = params => {
    return ajaxNew('/business/api.v1.question/qlabel/add', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                Message.success("新增成功");
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => { })
}

/* 
    删除标签配置
    http://10.169.128.35:3000/project/21/interface/api/10751
*/
const delTag = params => {
    return ajaxNew('/business/api.v1.question/qlabel/del', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                return res.resultMsg
            }
        })
        .catch((e) => { })
}

/* 
编辑标签配置
http://10.169.128.35:3000/project/21/interface/api/10749
*/
const editTag = params => {
    return ajaxNew('/business/api.v1.question/qlabel/mod', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                Message.success("编辑成功");
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => { })
}

/* 
    查询流程
    http://10.169.128.35:3000/project/21/interface/api/10761
*/
const qryFlow = params => {
    return ajaxNew('/business/api.v1.question/qflow/qryList', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data && res.data.items;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => { })
}

/* 
    失效流程
    http://10.169.128.35:3000/project/21/interface/api/10759
*/
const flowSetState = params => {
    return ajaxNew('/business/api.v1.question/qflow/changeState', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => { })
}

/* 
    修改流程
    http://10.169.128.35:3000/project/21/interface/api/10757
*/
const flowEdit = params => {
    return ajaxNew('/business/api.v1.question/qflow/mod', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                Message.success("操作成功");
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => { })
}


/* 
    http://10.169.128.35:3000/project/21/interface/api/10755
    增加流程
*/
const flowAdd = params => {
    return ajaxNew('/business/api.v1.question/qflow/add', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                Message.success("操作成功");
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => { })
}

/* 
    http://10.169.128.35:3000/project/36/interface/api/10861
    查询所有通知类型(QMS)
*/
const noticeModelQryAll = params => {
    return ajaxNew('/business/api.v1.send/noticeModel/qryAll', params, "post", {
        type: "ticSend",
    })
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => { })
}

/*
    保存样品分类
    接口地址：/ticCenter/business/api.v1.center/sampleCategory/save
*/
const saveSampleCategory = params => {
    return ajaxNew('/business/api.v1.center/sampleCategory/save', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                // 不在这里显示成功消息，让页面组件自己处理
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
    删除样品分类
    接口地址：/ticCenter/business/api.v1.center/sampleCategory/del/{categoryId}
*/
const delSampleCategory = categoryId => {
    return ajaxNew(`/business/api.v1.center/sampleCategory/del/${categoryId}`, {}, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                // 不在这里显示成功消息，让页面组件自己处理
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}

/*
    查询样品分类
    接口地址：/ticCenter/business/api.v1.center/sampleCategory/qry
*/
const qrySampleCategory = params => {
    return ajaxNew('/business/api.v1.center/sampleCategory/qry', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
    根据业务线查询样品分类
    接口地址：/ticCenter/business/api.v1.center/sampleStandard/qryByLine
*/
const qrySampleCategoryByLine = params => {
    return ajaxNew('/business/api.v1.center/sampleCategory/qryByLine', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                // 直接返回服务端数据，不添加硬编码的"全部"选项
                return res.data || [];
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
    样品分类排序
    接口地址：/ticCenter/business/api.v1.center/sampleCategory/transSort
*/
const transSortSampleCategory = params => {
    return ajaxNew('/business/api.v1.center/sampleCategory/transSort', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return true;
            } else {
                Message.error(res.resultMsg || '排序失败');
                return false
            }
        })
        .catch((e) => {
            Message.error('排序失败: ' + e);
            return false
        });
}

/*
    根据业务线查询样品标准
    接口地址：/ticCenter/business/api.v1.center/sampleStandard/qryByLine
*/
const qrySampleStandardByLine = params => {
    return ajaxNew('/business/api.v1.center/sampleStandard/qryByLine', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
    查询样品标准
    接口地址：/ticCenter/business/api.v1.center/sampleStandard/qry
*/
const qrySampleStandard = params => {
    return ajaxNew('/business/api.v1.center/sampleStandard/qry', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                return res.data;
            } else {
                Message.error(res.resultMsg);
                return []
            }
        })
        .catch((e) => {
            Message.error(e);
            return []
        });
}

/*
    保存样品标准
    接口地址：/ticCenter/business/api.v1.center/sampleStandard/save
*/
const saveSampleStandard = params => {
    return ajaxNew('/business/api.v1.center/sampleStandard/save', params, "post")
        .then((res) => {
            if (res.resultCode === "0") {
                // 不在这里显示成功消息，让页面组件自己处理
                return true;
            } else {
                Message.error(res.resultMsg);
                return false
            }
        })
        .catch((e) => {
            Message.error(e);
            return false
        });
}
export {
    qryFlow,
    flowSetState,
    flowEdit,
    flowAdd,
    qryTag,
    addTag,
    delTag,
    editTag,
    addressQry,
    qryAccount,
    qryBU,
    qryBuInfo,
    rnameBu,
    addSubBu,
    addBusiness,
    modBusiness,
    delBusiness,
    qryLaboratory,
    addLaboratory,
    modLaboratory,
    getLaboratory,
    qryInfoByLab,
    qryBusiness,
    qryByPositionAction,
    qryByChannel,
    qryEnum,
    qryByBusi,
    modSample,
    copyRelevanceGroup,
    groupQryList,
    groupAdd,
    groupMod,
    groupDel,
    groupShopQry,
    mailGroupQryList,
    mailGroupAdd,
    mailGroupDel,
    mailTemplateQry,
    mailTemplateQryDtl,
    personQry,
    itemCopy,
    qrySampleKey,
    noticeModelQryAll,
    qrySampleCategory,
    qrySampleCategoryByLine,
    qrySampleStandard,
    qrySampleStandardByLine,
    saveSampleCategory,
    delSampleCategory,
    saveSampleStandard
}
