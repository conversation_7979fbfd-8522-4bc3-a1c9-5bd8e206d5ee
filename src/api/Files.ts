import { ajaxExport } from "./index";
import { Message } from 'element-ui';

const exportTestItem = (params, fileName): void => {
  ajaxExport(
    "/business/api.v1.center/item/exp",
    params,
    "post"
  )
  .then((res) => {
    debugger
    let filename = fileName;
    // IE下载
    if (window.navigator.msSaveOrOpenBlob) {
      const blobObject = new Blob([res.data], {
        type: "application/actet-stream;charset=utf-8",
      });
      console.log(filename)
      window.navigator.msSaveBlob(blobObject, filename);
    } else {
      const contentDisposition = res.headers["content-disposition"]; //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
      if (contentDisposition) {
        const patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
        const result = patt.exec(contentDisposition);
        filename = decodeURIComponent(result[1]);
      }
      const blob = new Blob([res.data], {
        type: "application/actet-stream;charset=utf-8",
      });
      const downloadElement = document.createElement("a");
      const href = window.URL.createObjectURL(blob); //创建下载的链接
      downloadElement.style.display = "none";
      downloadElement.href = href;
      if (filename) {
        downloadElement.download = decodeURI(filename).replace(
          /\+/g,
          " "
        );
      } else {
        // downloadElement.download = "报表统计" + ".xls"; //下载后文件名
      }
      document.body.appendChild(downloadElement);
      downloadElement.click(); //点击下载
      setTimeout(() => {
        document.body.removeChild(downloadElement); //下载完成移除元素
        window.URL.revokeObjectURL(href); //释放掉blob对象
      }, 1000);
    }
  })
  .catch((error) => {
    Message.error(error);
  });
}
export {
  exportTestItem
}