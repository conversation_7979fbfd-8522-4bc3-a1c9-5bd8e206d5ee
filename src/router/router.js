import Router from 'vue-router'
import routes from './routes'
import store from './../store'
import {
  local,
  CurrentSystem
} from '@/assets/javascript/util'
import {
  Message
} from 'element-ui'

export default () => {
  const router = new Router({
    routes,
    mode: 'history',
    linkActiveClass: 'active-link',
    linkExactActiveClass: 'exact-active-link',
    scrollBehavior(to, from, savedPosition) {
      if (savedPosition) {
        return savedPosition
      } else {
        return {
          x: 0,
          y: 0
        }
      }
    },
    fallback: true
  })

  // 全局路由守卫,没有登录返回首页
  router.beforeEach((to, from, next) => {
    let title = to.meta.title ? to.meta.title : document.title
    console.log(process.env.NODE_ENV);
    if (process.env.NODE_ENV !== 'prodGray' && process.env.NODE_ENV !== 'prod' && process.env.NODE_ENV !== 'product') {
      title = '测试环境' + process.env.NODE_ENV + '-' + title
    }
    document.title = title;
    if (!local.get('token')) {
      if (to.name === 'Login') {
        next()
      } else {
        next({
          path: '/login?redirect=' + to.path
        })
      }
    } else {
      // 已经登录后还访问了登录页面
      if (to.name === 'Login') {
        next({
          path: '/home'
        })
      } else {
        // 已经登录，没有权限
        if (!store.state.userRights) {
          // 根据后台数据生成权限路由
          store.dispatch("getUserRights", {
            system: CurrentSystem,
          }).then(res => {
            if (res) {
              if (to.meta.title) {
                /* 路由发生变化修改页面title */
                document.title = to.meta.title;
              }
              // tips ...to参数必传，否则路由不能正常加载。
              next({
                ...to,
                replace: true
              })
            } else {
              // 如果是样品分类页面，即使权限获取失败也允许访问
              if (to.path === '/business/sampleClassify') {
                console.log('样品分类页面，允许访问')
                next()
              } else {
                Message.error('系统异常，请稍后重试。');
              }
            }
          }).catch(e => {
            console.error('权限获取失败:', e)
            // 如果是样品分类页面，即使权限获取失败也允许访问
            if (to.path === '/business/sampleClassify') {
              console.log('样品分类页面，权限获取失败但允许访问')
              next()
            } else {
              Message.error(e);
            }
          })
        } else {
          // 已登录，并且有权限
          if (to.meta.title) {
            /* 路由发生变化修改页面title */
            document.title = to.meta.title;
          }
          next()
        }
      }
    }
  })
  return router
}
