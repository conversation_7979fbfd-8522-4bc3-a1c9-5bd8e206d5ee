// const meta = {
//   requireAuth: true
// };
export default [{
    path: '/',
    name: 'index',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import( /* webpackChunkName: "Login" */ '@/views/Login'),
    meta: {
      title: "登录"
    }
  },
  {
    path: '/step0',
    name: 'Step0',
    component: () => import( /* webpackChunkName: "Step" */ '@/views/questionnaire/Step0'),
    meta: {
      title: "流程配置"
    }
  },
  {
    path: '/step1',
    name: 'Step1',
    component: () => import( /* webpackChunkName: "Step" */ '@/views/questionnaire/Step1'),
    meta: {
      title: "选择包含商品类目"
    }
  },
  {
    path: '/step2',
    name: 'Step2',
    component: () => import( /* webpackChunkName: "Step" */ '@/views/questionnaire/Step2'),
    meta: {
      title: "配置首页问题"
    }
  },
  {
    path: '/step3',
    name: 'Step3',
    component: () => import( /* webpackChunkName: "Step" */ '@/views/questionnaire/Step3'),
    meta: {
      title: "配置推荐项目"
    }
  },
  {
    path: '/cardRelevance',
    name: 'CardRelevance',
    component: () => import( /* webpackChunkName: "CardRelevance" */ '@/views/questionnaire/CardRelevance'),
    meta: {
      title: "项目问题编辑"
    }
  },
  {
    path: '/relevanceGroup',
    name: 'RelevanceGroup',
    component: () => import( /* webpackChunkName: "RelevanceGroup" */ '@/views/questionnaire/RelevanceGroup'),
    meta: {
      title: "项目关联规则"
    }
  },
  {
    path: '/step4',
    name: 'Step4',
    component: () => import( /* webpackChunkName: "Step4" */ '@/views/questionnaire/Step4'),
    meta: {
      title: "补充信息"
    }
  },
  {
    path: '/preview',
    name: 'preview',
    component: () => import( /* webpackChunkName: "preview" */ '@/views/Preview'),
    meta: {
      title: "预览"
    }
  },
  {
    path: '/frame',
    name: 'frame',
    component: () => import( /* webpackChunkName: "frame" */ '@/views/Frame'),
    children: [{
        path: '/home',
        name: 'home',
        component: () => import( /* webpackChunkName: "home" */ '@/views/Home'),
        meta: {
          title: "系统首页",
          requireAuth: true
        }
      },
      {
        path: '/questionnaire/list',
        name: 'QuestionnaireList',
        component: () => import( /* webpackChunkName: "questionnaire" */ '../views/questionnaire/List.vue'),
        meta: {
          title: "问题列表",
          requireAuth: true
        }
      },
      {
        path: '/category',
        name: 'Category',
        component: () => import( /* webpackChunkName: "category" */ '../views/category/Index.vue'),
        meta: {
          title: "分类目录",
          requireAuth: true
        }
      },
      {
        path: '/buManage',
        name: 'buManage',
        component: () => import( /* webpackChunkName: "buManage" */ '../views/config/BuManage.vue'),
        meta: {
          title: "BU维护",
          requireAuth: true
        }
      },
      {
        path: '/business',
        name: 'Business',
        component: () => import( /* webpackChunkName: "business" */ '../views/config/BusinessLine.vue'),
        meta: {
          title: "业务线维护",
          requireAuth: true
        }
      },
	  {
		path: '/business/sampleClassify',
		name: 'SampleClassify',
		component: () => import( /* webpackChunkName: "sampleClassify" */ '../views/config/SampleClassify.vue'),
		meta: {
		  title: "样品分类维护",
		  requireAuth: false
		}
	  },
      {
        path: '/laboratory',
        name: 'Laboratory',
        component: () => import( /* webpackChunkName: "laboratory" */ '../views/config/Laboratory.vue'),
        meta: {
          title: "实验室维护",
          requireAuth: true
        }
      },
      {
        path: '/lable/config',
        name: 'LableConfig',
        component: () => import( /* webpackChunkName: "laboratory" */ '../views/config/Lable.vue'),
        meta: {
          title: "标签配置",
          requireAuth: true
        }
      },
      {
        path: '/group/rights',
        name: 'groupRights',
        component: () => import( /* webpackChunkName: "group" */ '../views/group/Rights.vue'),
        meta: {
          title: "权限组",
          requireAuth: true
        }
      },
      {
        path: '/group/email',
        name: 'groupEmail',
        component: () => import( /* webpackChunkName: "group" */ '../views/group/Email.vue'),
        meta: {
          title: "邮件组",
          requireAuth: true
        }
      },
      // {
      //   path: '/business',
      //   name: 'business',
      //   component: () => import( /* webpackChunkName: "business" */ '../views/bu/Business.vue'),
      //   meta: {
      //     title: "业务线维护",
      //     requireAuth: true
      //   }
      // },
      // {
      //   path: '/laboratory',
      //   name: 'laboratory',
      //   component: () => import( /* webpackChunkName: "laboratory" */ '../views/bu/Laboratory.vue'),
      //   meta: {
      //     title: "实验室维护",
      //     requireAuth: true
      //   }
      // },
      {
        path: '/testItem',
        name: 'testItem',
        component: () => import( /* webpackChunkName: "testItem" */ '../views/testItem/Index.vue'),
        meta: {
          title: "测试项目配置",
          requireAuth: true
        }
      },
      {
        path: '/sms/list',
        name: 'SMSlist',
        component: () => import( /* webpackChunkName: "SMSlist" */ '../views/template/sms/List.vue'),
        meta: {
          title: "短信模板",
          requireAuth: true
        }
      },
      {
        path: '/mail/list',
        name: 'mailList',
        component: () => import( /* webpackChunkName: "mailList" */ '../views/template/mail/List.vue'),
        meta: {
          title: "邮件模板",
          requireAuth: true
        }
      },
    ]
  },
  {
    path: '*',
    name: '404',
    redirect: '/'
  }
]