{"name": "tic-center-web", "code": "project_tic", "version": "2025.07.31", "description": "该项目是数据配置中心，使用vue2配合Typescript使用，集成ElementUi做为ui库，主要功能包含OIQ问卷配置功能，BU配置，实验室配置，业务线配置以及短信及邮件模板。同时还囊括了邮件组及权限组", "author": "Ken<PERSON>@sgs.com", "private": true, "scripts": {"local": "vue-cli-service serve --mode test", "dev": "vue-cli-service build --mode development", "test": "vue-cli-service build --mode test --max-old-space-size=4096", "uat": "vue-cli-service build --mode uat && rm -rf ./dist/*.map", "prod": "vue-cli-service build --mode prod && rm -rf ./dist/*.map", "azureprod": "vue-cli-service build --mode prod && rm -rf ./dist/*.map", "gray": "vue-cli-service build --mode gray", "prodGray": "vue-cli-service build --mode prodGray", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@sentry/tracing": "^7.45.0", "@sentry/vue": "^7.45.0", "@tic/web-monitor": "^24.8.6", "@tinymce/tinymce-vue": "3.2.3", "@types/tinymce": "4.6.0", "axios": "0.19.2", "babel-polyfill": "6.26.0", "core-js": "3.6.5", "crypto-md5": "1.0.0", "element-ui": "2.15.14", "less": "3.11.3", "less-loader": "6.1.1", "path": "0.12.7", "tinymce": "5.5.1", "tinymce-vue": "1.0.0", "vue": "2.6.11", "vue-class-component": "7.2.3", "vue-property-decorator": "8.4.2", "vue-router": "3.2.0", "vuedraggable": "^2.17.0", "vuex": "3.4.0", "vuex-class": "0.3.2"}, "devDependencies": {"@sentry/webpack-plugin": "1.20.0", "@typescript-eslint/eslint-plugin": "2.33.0", "@typescript-eslint/parser": "2.33.0", "@vue/cli-plugin-babel": "4.4.0", "@vue/cli-plugin-eslint": "4.4.0", "@vue/cli-plugin-router": "4.4.0", "@vue/cli-plugin-typescript": "4.4.0", "@vue/cli-plugin-vuex": "4.4.0", "@vue/cli-service": "4.4.0", "@vue/eslint-config-typescript": "5.0.2", "compression-webpack-plugin": "1.1.12", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "typescript": "3.9.3", "uglifyjs-webpack-plugin": "^2.2.0", "vue-cli-plugin-element": "1.0.1", "vue-template-compiler": "2.6.11"}, "sideEffects": false, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": "./cz.config.js"}}}